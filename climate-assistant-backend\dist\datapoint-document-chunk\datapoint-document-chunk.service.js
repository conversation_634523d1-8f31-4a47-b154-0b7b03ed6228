"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DatapointDocumentChunkService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatapointDocumentChunkService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const throat_1 = require("throat");
const prompts_service_1 = require("../prompts/prompts.service");
const document_chunk_entity_1 = require("../document/entities/document-chunk.entity");
const datapoint_request_entity_1 = require("../datapoint/entities/datapoint-request.entity");
const datapoint_document_chunk_entity_1 = require("./entities/datapoint-document-chunk.entity");
const document_entity_1 = require("../document/entities/document.entity");
const esrs_disclosure_requirement_entity_1 = require("../esrs/entities/esrs-disclosure-requirement.entity");
const users_service_1 = require("../users/users.service");
const constants_1 = require("../constants");
const llm_rate_limiter_service_1 = require("../llm/services/llm-rate-limiter.service");
const logger_service_1 = require("../shared/logger.service");
let DatapointDocumentChunkService = DatapointDocumentChunkService_1 = class DatapointDocumentChunkService {
    constructor(llmRateLimitService, dataSource, promptService, documentChunkRepository, documentRepository, datapointRequestRepository, datapointDocumentChunkRepository, esrsDisclosureRequirementRepository, userService) {
        this.llmRateLimitService = llmRateLimitService;
        this.dataSource = dataSource;
        this.promptService = promptService;
        this.documentChunkRepository = documentChunkRepository;
        this.documentRepository = documentRepository;
        this.datapointRequestRepository = datapointRequestRepository;
        this.datapointDocumentChunkRepository = datapointDocumentChunkRepository;
        this.esrsDisclosureRequirementRepository = esrsDisclosureRequirementRepository;
        this.userService = userService;
        this.logger = new logger_service_1.WorkerLogger(DatapointDocumentChunkService_1.name);
        this.chunkProcessingLimit = (0, throat_1.default)(10);
        this.drProcessingLimit = (0, throat_1.default)(20);
        this.esrsDisclosureRequirementsCache = null;
        this.cacheTimestamp = 0;
        this.CACHE_TTL = 5 * 60 * 1000;
    }
    async linkDocumentChunksToDatapoints(documentId, userId, maxNumberOfChunks) {
        this.logger.log(`Start linking Datapoints to Chunks for DocumentId ${documentId}`);
        let document;
        let projectId;
        try {
            document = await this.documentRepository.findOne({
                where: { id: documentId },
                relations: {
                    workspace: {
                        projects: true,
                    },
                },
            });
            if (!document) {
                this.logger.error(`Document ${documentId} not found in database`);
                return;
            }
            projectId = document.workspace?.projects[0]?.id;
            if (!projectId) {
                this.logger.error(`Document ${documentId} does not have a project`);
                document.status = document_entity_1.DocumentStatus.ErrorProcessing;
                await this.documentRepository.save(document);
                return;
            }
            this.logger.log(`Start linking Datapoints to Chunks for DocumentId ${documentId}, ProjectId ${projectId}`);
            document.status = document_entity_1.DocumentStatus.LinkingData;
            await this.documentRepository.save(document);
        }
        catch (error) {
            this.logger.error(`Failed to initialize document linking for ${documentId}:`, error instanceof Error ? error.stack : error);
            return;
        }
        this.logger.log(`Start linking Datapoints to Chunks for DocumentId ${documentId}, ProjectId ${projectId}`);
        document.status = document_entity_1.DocumentStatus.LinkingData;
        await this.documentRepository.save(document);
        try {
            const documentChunks = await this.documentChunkRepository.find({
                where: {
                    documentId: documentId,
                },
                order: {
                    page: 'ASC',
                },
                select: ['id', 'content', 'page', 'matchingsJson'],
            });
            this.logger.log(`${documentId}: Found ${documentChunks.length} Document Chunks for Document`);
            const response = {
                inputTokensUsed: 0,
                outputTokensUsed: 0,
                costForDocument: 0,
                totalLinksCreated: 0,
                timeToGenerate: '0s',
                numberOfChunks: documentChunks.length,
                chunks: [],
            };
            const startTime = Date.now();
            const createdAtBatch = new Date();
            const chunksToProcess = documentChunks.slice(0, maxNumberOfChunks || documentChunks.length);
            const esrsDisclosrueRequirements = await this.getCachedEsrsDisclosureRequirements();
            const globalUser = await this.userService.findGlobalGlacierAIUser();
            const allDatapointRequests = await this.datapointRequestRepository
                .createQueryBuilder('datapointRequest')
                .innerJoinAndSelect('datapointRequest.esrsDatapoint', 'esrsDatapoint')
                .innerJoinAndSelect('datapointRequest.dataRequest', 'dataRequest')
                .where('dataRequest.projectId = :projectId', { projectId })
                .getMany();
            const datapointRequestMap = new Map();
            allDatapointRequests.forEach((dpr) => {
                datapointRequestMap.set(dpr.esrsDatapoint.datapointId, dpr);
            });
            const BATCH_SIZE = 20;
            const chunkBatches = [];
            for (let i = 0; i < chunksToProcess.length; i += BATCH_SIZE) {
                chunkBatches.push(chunksToProcess.slice(i, i + BATCH_SIZE));
            }
            let processedBatches = 0;
            for (const batch of chunkBatches) {
                try {
                    this.logger.log(`${documentId}: Processing batch ${processedBatches + 1}/${chunkBatches.length} (${batch.length} chunks)`);
                    const batchPromises = batch.map((chunk) => {
                        return this.chunkProcessingLimit(async () => {
                            try {
                                await this.processChunk(chunk, documentId, esrsDisclosrueRequirements, datapointRequestMap, globalUser?.id || userId, createdAtBatch, response, projectId);
                            }
                            catch (chunkError) {
                                this.logger.error(`${documentId}: Error processing chunk ${chunk.id}:`, chunkError instanceof Error ? chunkError.stack : chunkError);
                            }
                        });
                    });
                    await Promise.all(batchPromises);
                    const chunksToSave = batch.filter((chunk) => chunk.matchingsJson);
                    if (chunksToSave.length > 0) {
                        await this.documentChunkRepository.save(chunksToSave);
                    }
                    processedBatches++;
                }
                catch (batchError) {
                    this.logger.error(`${documentId}: Error processing batch ${processedBatches + 1}:`, batchError instanceof Error ? batchError.stack : batchError);
                }
            }
            this.logger.log(`TOTAL TOKEN USAGE FOR EXTRACTION ${documentId}: inputTokensUsed ${response.inputTokensUsed}, outputTokensUsed: ${response.outputTokensUsed}, costFordocument: ${response.costForDocument}`);
            const endTime = Date.now();
            response.timeToGenerate = `${(endTime - startTime) / 1000}s`;
            document.status = document_entity_1.DocumentStatus.LinkingDataFinished;
            await this.documentRepository.save(document);
            this.logger.log(`DatapointLinking Finished ${documentId}: ${document.status}`);
        }
        catch (error) {
            const documentStillExists = await this.documentRepository.findOne({
                where: { id: documentId },
            });
            if (!documentStillExists) {
                this.logger.error(`Document ${documentId} was deleted during linking process. Ending processing.`);
                return;
            }
            this.logger.error(`Critical error in linkDocumentChunksToDatapoints for document ${documentId}:`);
            if (error instanceof Error) {
                this.logger.error(`Error name: ${error.name}`);
                this.logger.error(`Error message: ${error.message}`);
                this.logger.error(`Error stack: ${error.stack}`);
            }
            else {
                this.logger.error(`Unknown error type: ${JSON.stringify(error)}`);
            }
            if (error instanceof typeorm_2.QueryFailedError) {
                this.logger.error(`Database query failed: ${error.query}`);
                this.logger.error(`Query parameters: ${JSON.stringify(error.parameters)}`);
            }
            if (error?.response?.status === 429) {
                this.logger.error('Rate limit exceeded from LLM provider');
            }
            if (error?.code === 'ECONNREFUSED' || error?.code === 'ETIMEDOUT') {
                this.logger.error('Network connection error');
            }
            document.status = document_entity_1.DocumentStatus.ErrorProcessing;
            await this.documentRepository.save(document);
            throw error;
        }
    }
    async getCachedEsrsDisclosureRequirements() {
        const now = Date.now();
        if (!this.esrsDisclosureRequirementsCache ||
            now - this.cacheTimestamp > this.CACHE_TTL) {
            this.esrsDisclosureRequirementsCache =
                await this.esrsDisclosureRequirementRepository.find({
                    relations: ['esrsDatapoints'],
                });
            this.cacheTimestamp = now;
        }
        return this.esrsDisclosureRequirementsCache;
    }
    async processChunk(chunk, documentId, esrsDisclosrueRequirements, datapointRequestMap, userId, createdAtBatch, response, projectId) {
        this.logger.debug(`${documentId}: Linking chunk ${chunk.id}, Length: ${chunk.content.length}`);
        const chunkContent = chunk.content;
        if (chunkContent.length < 100) {
            this.logger.debug(`${documentId}: Skipping chunk ${chunk.id} - content too short`);
            return;
        }
        this.logger.debug(`${documentId}: ${chunk.id} Start Disclosure Requirement Classification`);
        const disclosureRequirementClassificationChatCompletion = [
            {
                role: 'system',
                content: this.promptService.generateDisclosureRequirementClassificationReasoningPrompt({
                    chunkContent,
                    esrsDisclosureRequirements: esrsDisclosrueRequirements,
                }),
            },
        ];
        let disclosureRequirementClassificationResponse;
        try {
            disclosureRequirementClassificationResponse =
                await this.llmRateLimitService.handleRequest({
                    model: constants_1.LLM_MODELS['o4-mini'],
                    messages: disclosureRequirementClassificationChatCompletion,
                    json: true,
                    temperature: 0,
                });
        }
        catch (llmError) {
            this.logger.error(`${documentId}: Chunk ${chunk.id} - LLM request failed for DR classification:`, llmError instanceof Error ? llmError.stack : llmError);
            throw new Error(`LLM request failed for chunk ${chunk.id}: ${llmError?.message || 'Unknown error'}`);
        }
        if (disclosureRequirementClassificationResponse.status === 400) {
            this.logger.error(`${documentId}: Chunk: ${chunk.id} DR Classification failed with status 400: ${JSON.stringify(disclosureRequirementClassificationResponse.response)}`);
            return;
        }
        if (!disclosureRequirementClassificationResponse.response) {
            this.logger.error(`${documentId}: Chunk ${chunk.id} - Empty response from LLM for DR classification`);
            return;
        }
        response.inputTokensUsed +=
            disclosureRequirementClassificationResponse.token.prompt_tokens || 0;
        response.outputTokensUsed +=
            disclosureRequirementClassificationResponse.token.completion_tokens || 0;
        response.costForDocument +=
            disclosureRequirementClassificationResponse.token.total_cost || 0;
        const disclosureRequirementMatches = disclosureRequirementClassificationResponse.response.disclosureRequirementMatches?.map((dr) => {
            const cleanMatchedId = dr
                .replace('ESRS 2.', '')
                .replace('ESRS', '')
                .replace(/'/g, '')
                .replace(/"/g, '')
                .replace(/\s/g, '');
            return cleanMatchedId;
        }) || [];
        const chunkMatches = {
            chunkContent: chunkContent,
            topicMatches: [],
            disclosureRequirementMatches: disclosureRequirementMatches,
            datapointMatches: [],
        };
        if (disclosureRequirementMatches &&
            disclosureRequirementMatches.length > 0) {
            this.logger.debug(`${documentId}: Chunk: ${chunk.id} DRsMatched: ${JSON.stringify(disclosureRequirementMatches.join(', '))}`);
            const drPromises = disclosureRequirementMatches.map((dr) => {
                return this.drProcessingLimit(async () => {
                    try {
                        await this.processDRForChunk(dr, chunk, chunkContent, documentId, esrsDisclosrueRequirements, datapointRequestMap, userId, createdAtBatch, response, chunkMatches, projectId);
                    }
                    catch (drError) {
                        this.logger.error(`${documentId}: Chunk ${chunk.id} - Error processing DR ${dr}:`, drError instanceof Error ? drError.stack : drError);
                    }
                });
            });
            await Promise.all(drPromises);
            chunk.matchingsJson = JSON.stringify(chunkMatches);
            response.chunks.push(chunkMatches);
        }
    }
    async processDRForChunk(dr, chunk, chunkContent, documentId, esrsDisclosrueRequirements, datapointRequestMap, userId, createdAtBatch, response, chunkMatches, projectId) {
        this.logger.debug(`${documentId}: Chunk: ${chunk.id} DR Matching started: ${dr}`);
        const esrsDisclosureRequirementMatch = esrsDisclosrueRequirements.find((eDr) => eDr.dr === dr);
        if (!esrsDisclosureRequirementMatch) {
            this.logger.error(`${documentId}: Chunk: ${chunk.id} DR: ${dr} not found in the workspace`);
            return;
        }
        const esrsDatapointsToMatch = esrsDisclosureRequirementMatch.esrsDatapoints;
        if (esrsDatapointsToMatch?.length > 0) {
            let datapointMatches = [];
            let retryCount = 0;
            const maxRetries = 1;
            while (retryCount <= maxRetries) {
                const datapointClassificationChatCompletion = [
                    {
                        role: 'system',
                        content: this.promptService.generateDatapointClassificationReasoningPrompt({
                            chunkContent,
                            esrsDatapoints: esrsDatapointsToMatch,
                            disclosureRequirement: esrsDisclosureRequirementMatch,
                        }),
                    },
                ];
                let datapointClassificationResponse;
                try {
                    datapointClassificationResponse =
                        await this.llmRateLimitService.handleRequest({
                            model: constants_1.LLM_MODELS['o4-mini'],
                            messages: datapointClassificationChatCompletion,
                            json: true,
                            temperature: 0,
                        });
                }
                catch (llmError) {
                    this.logger.error(`${documentId}: Chunk ${chunk.id} DR ${dr} - LLM request failed for datapoint classification:`, llmError instanceof Error ? llmError.stack : llmError);
                    throw new Error(`LLM request failed for DR ${dr}: ${llmError?.message || 'Unknown error'}`);
                }
                if (datapointClassificationResponse.status === 400) {
                    this.logger.error(`${documentId}: Chunk: ${chunk.id} DR: ${dr} DP Classification failed with status 400: ${JSON.stringify(datapointClassificationResponse.response)}`);
                    return;
                }
                if (!datapointClassificationResponse.response) {
                    this.logger.error(`${documentId}: Chunk ${chunk.id} DR ${dr} - Empty response from LLM for datapoint classification`);
                    return;
                }
                response.inputTokensUsed +=
                    datapointClassificationResponse.token.prompt_tokens || 0;
                response.outputTokensUsed +=
                    datapointClassificationResponse.token.completion_tokens || 0;
                response.costForDocument +=
                    datapointClassificationResponse.token.total_cost || 0;
                datapointMatches =
                    datapointClassificationResponse.response.matchedDatapoints || [];
                this.logger.debug(`${documentId}: Chunk: ${chunk.id} DR: ${dr} DPs matched: ${JSON.stringify(datapointMatches)}`);
                if (datapointMatches && datapointMatches.length > 0) {
                    let anyMatchSuccessful = false;
                    const failedMatches = [];
                    for (const datapointMatch of datapointMatches) {
                        if (datapointMatch.matchedId) {
                            const matchingDatapointRequest = datapointRequestMap.get(datapointMatch.matchedId);
                            if (!matchingDatapointRequest) {
                                failedMatches.push(datapointMatch.matchedId);
                                this.logger.error(`${documentId}: Chunk: ${chunk.id} Datapoint: ${datapointMatch.matchedId} not found in the workspace`);
                            }
                            else {
                                anyMatchSuccessful = true;
                            }
                        }
                    }
                    if (!anyMatchSuccessful && retryCount < maxRetries) {
                        retryCount++;
                        this.logger.warn(`${documentId}: Chunk: ${chunk.id} DR: ${dr} All datapoint matches failed (${failedMatches.join(', ')}). Retrying... (attempt ${retryCount + 1}/${maxRetries + 1})`);
                        datapointMatches = [];
                        continue;
                    }
                    chunkMatches.datapointMatches.push(...datapointMatches);
                    this.logger.debug(`${documentId}: Chunk: ${chunk.id} DR: ${dr} DPMatched: ${JSON.stringify(datapointMatches.map((dp) => dp.matchedId).join(', '))}`);
                    const datapointDocumentChunksToCreate = [];
                    const datapointRequestsToUpdate = [];
                    for (const datapointMatch of datapointMatches) {
                        if (datapointMatch.matchedId) {
                            datapointMatch.executedClassificationPrompt = true;
                            const matchingDatapointRequest = datapointRequestMap.get(datapointMatch.matchedId);
                            if (!matchingDatapointRequest) {
                                continue;
                            }
                            datapointDocumentChunksToCreate.push({
                                documentChunkId: chunk.id,
                                datapointRequestId: matchingDatapointRequest.id,
                                createdBy: userId,
                                createdAt: createdAtBatch,
                            });
                            if (datapointMatch.mdrTitle) {
                                const cleanPolicy = datapointMatch.mdrTitle
                                    .replace(/<\/?h6>/g, '')
                                    .trim();
                                if (!matchingDatapointRequest.customUserRemark ||
                                    !matchingDatapointRequest.customUserRemark.includes(cleanPolicy)) {
                                    if (!matchingDatapointRequest.customUserRemark ||
                                        matchingDatapointRequest.customUserRemark.length < 12) {
                                        matchingDatapointRequest.customUserRemark =
                                            'Write among others about the following Policies/Target(s)/Action(s):' +
                                                `\n- ${cleanPolicy}`;
                                    }
                                    else {
                                        matchingDatapointRequest.customUserRemark += `\n- ${cleanPolicy}`;
                                    }
                                    datapointRequestsToUpdate.push(matchingDatapointRequest);
                                }
                            }
                        }
                    }
                    if (datapointDocumentChunksToCreate.length > 0) {
                        try {
                            const insertResult = await this.datapointDocumentChunkRepository
                                .createQueryBuilder()
                                .insert()
                                .into(datapoint_document_chunk_entity_1.DatapointDocumentChunk)
                                .values(datapointDocumentChunksToCreate)
                                .orIgnore()
                                .execute();
                            response.totalLinksCreated += insertResult.identifiers.length;
                            this.logger.debug(`${documentId}: Chunk ${chunk.id} DR ${dr} - Created ${insertResult.identifiers.length} datapoint-document-chunk links`);
                        }
                        catch (insertError) {
                            this.logger.error(`${documentId}: Chunk ${chunk.id} DR ${dr} - Batch insert error:`, insertError instanceof Error ? insertError.stack : insertError);
                        }
                    }
                    if (datapointRequestsToUpdate.length > 0) {
                        try {
                            await this.datapointRequestRepository.save(datapointRequestsToUpdate);
                            this.logger.debug(`${documentId}: Chunk ${chunk.id} DR ${dr} - Updated ${datapointRequestsToUpdate.length} datapoint requests`);
                        }
                        catch (updateError) {
                            this.logger.error(`${documentId}: Chunk ${chunk.id} DR ${dr} - Failed to update datapoint requests:`, updateError instanceof Error ? updateError.stack : updateError);
                        }
                    }
                    break;
                }
                else {
                    break;
                }
            }
        }
    }
};
exports.DatapointDocumentChunkService = DatapointDocumentChunkService;
exports.DatapointDocumentChunkService = DatapointDocumentChunkService = DatapointDocumentChunkService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_1.InjectDataSource)()),
    __param(3, (0, typeorm_1.InjectRepository)(document_chunk_entity_1.DocumentChunk)),
    __param(4, (0, typeorm_1.InjectRepository)(document_entity_1.Document)),
    __param(5, (0, typeorm_1.InjectRepository)(datapoint_request_entity_1.DatapointRequest)),
    __param(6, (0, typeorm_1.InjectRepository)(datapoint_document_chunk_entity_1.DatapointDocumentChunk)),
    __param(7, (0, typeorm_1.InjectRepository)(esrs_disclosure_requirement_entity_1.ESRSDisclosureRequirement)),
    __metadata("design:paramtypes", [llm_rate_limiter_service_1.LlmRateLimiterService,
        typeorm_2.DataSource,
        prompts_service_1.PromptService,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        users_service_1.UsersService])
], DatapointDocumentChunkService);
//# sourceMappingURL=datapoint-document-chunk.service.js.map