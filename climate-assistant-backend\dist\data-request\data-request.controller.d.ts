import { DataRequestService } from './data-request.service';
import { GenerateDataRequestReportTextTextPayload, UpdateDataRequestPayload } from './entities/data-request.dto';
import { Comment } from 'src/project/entities/comment.entity';
import { Response } from 'express';
import { Observable } from 'rxjs';
import { dataRequestGenerationStatus } from './entities/datarequest-generation.entity';
export declare class DataRequestController {
    private readonly dataRequestService;
    constructor(dataRequestService: DataRequestService);
    getDataRequest(req: any, dataRequestId: string): Promise<import("./entities/data-request.dto").DataRequestData>;
    updateDataRequest(req: any, dataRequestId: string, updateDataRequestPayload: UpdateDataRequestPayload): Promise<import("./entities/data-request.entity").DataRequest>;
    updateDataRequestStatus(req: any, dataRequestId: string, updateDataRequestPayload: UpdateDataRequestPayload): Promise<import("./entities/data-request.entity").DataRequest>;
    approveDataRequest(req: any, dataRequestId: string): Promise<import("./entities/data-request.entity").DataRequest>;
    reviewContentWithAi(dataRequestId: string, req: any): Promise<Comment[]>;
    generateContentWithAi(dataRequestId: string, data: GenerateDataRequestReportTextTextPayload, req: any): Promise<{
        content: string;
        id: string;
    }>;
    reviewBulkDatapointForDataRequest(dataRequestId: string, req: any): Promise<void>;
    generateBulkDatapointForDataRequest(dataRequestId: string, req: any): Promise<void>;
    getGenerations(dataRequestId: string): Promise<import("./entities/datarequest-generation.entity").DataRequestGeneration[]>;
    subscribeToDataRequestEvents(dataRequestId: string, response: Response): Observable<MessageEvent>;
    updateDatapointGenerationStatus(req: any, payload: {
        dataRequestGenerationId: string;
        status: dataRequestGenerationStatus;
        evaluatorComment?: string;
    }): Promise<{
        content?: string;
        status: dataRequestGenerationStatus;
    }>;
}
