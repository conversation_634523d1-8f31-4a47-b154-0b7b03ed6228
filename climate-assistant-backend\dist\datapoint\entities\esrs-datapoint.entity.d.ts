import { ESRSTopicDatapoint } from '../../esrs/entities/esrs-topic-datapoint.entity';
import { ESRSDisclosureRequirement } from '../../esrs/entities/esrs-disclosure-requirement.entity';
export declare class ESRSDatapoint {
    id: number;
    datapointId: string;
    name: string;
    optional: boolean;
    conditional: boolean;
    paragraph?: string;
    relatedAR?: string;
    lawText?: string;
    lawTextAR?: string;
    footnotes?: string;
    footnotesAR?: string;
    dataType?: string;
    publicAccess: boolean;
    createdAt: Date;
    esrsDisclosureRequirementId: number;
    exampleOutput: string;
    updatedAt: Date;
    esrsDisclosureRequirement: ESRSDisclosureRequirement;
    topicRelations: ESRSTopicDatapoint[];
}
