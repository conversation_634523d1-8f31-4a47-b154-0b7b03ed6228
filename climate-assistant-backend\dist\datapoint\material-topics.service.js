"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MaterialTopicsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const esrs_topic_datapoint_entity_1 = require("../esrs/entities/esrs-topic-datapoint.entity");
const project_service_1 = require("../project/project.service");
let MaterialTopicsService = class MaterialTopicsService {
    constructor(esrsTopicDatapointRepository, projectService) {
        this.esrsTopicDatapointRepository = esrsTopicDatapointRepository;
        this.projectService = projectService;
    }
    async loadMaterialTopics(datapointRequest) {
        const { id } = datapointRequest.esrsDatapoint;
        const projectId = datapointRequest.dataRequest.projectId;
        const esrsTopicDatapoint = await this.esrsTopicDatapointRepository.find({
            where: { esrsDatapointId: id },
            relations: ['topic'],
        });
        const validMaterialTopics = await this.generateHierarchicalListOfTopics({
            topicRelations: esrsTopicDatapoint,
            projectId,
            material: true,
        });
        return validMaterialTopics;
    }
    async generateHierarchicalListOfTopics({ topicRelations, projectId, material, }) {
        const projectEsrsTopics = await this.projectService.getProjectEsrsTopics({
            projectId,
            esrsTopicIds: topicRelations.map((tr) => tr.topic.id),
        });
        const filteredTopics = projectEsrsTopics.filter((mt) => Boolean(mt.active) === material);
        const filteredTopicIds = new Set(filteredTopics.map((mt) => mt.esrsTopicId));
        const topicsById = new Map();
        for (const tr of topicRelations) {
            if (filteredTopicIds.has(tr.topic.id)) {
                topicsById.set(tr.topic.id, { ...tr.topic, children: [] });
            }
        }
        for (const [, topic] of topicsById.entries()) {
            if (topic.parentId && !topicsById.has(topic.parentId)) {
                topic.parentId = null;
            }
        }
        for (const [, topic] of topicsById.entries()) {
            if (topic.parentId && topicsById.has(topic.parentId)) {
                topicsById.get(topic.parentId).children.push(topic);
            }
        }
        const hierarchicalTopics = [...topicsById.values()].filter((t) => !t.parentId);
        return hierarchicalTopics;
    }
};
exports.MaterialTopicsService = MaterialTopicsService;
exports.MaterialTopicsService = MaterialTopicsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(esrs_topic_datapoint_entity_1.ESRSTopicDatapoint)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        project_service_1.ProjectService])
], MaterialTopicsService);
//# sourceMappingURL=material-topics.service.js.map