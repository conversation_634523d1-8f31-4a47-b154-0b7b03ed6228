"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatapointRequestController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const datapoint_request_service_1 = require("./datapoint-request.service");
const common_2 = require("@nestjs/common");
const datapoint_request_guard_1 = require("./datapoint-request.guard");
const constants_1 = require("../constants");
const shared_datapoint_datarequest_service_1 = require("../shared/shared-datapoint-datarequest.service");
const permission_guard_1 = require("../auth/guard/permission.guard");
const permissions_decorator_1 = require("../auth/decorators/permissions.decorator");
const auth_guard_1 = require("../auth/auth.guard");
const datapoint_citation_service_1 = require("./datapoint-citation.service");
const datapoint_status_service_1 = require("./datapoint-status.service");
const datapoint_document_service_1 = require("./datapoint-document.service");
const material_topics_service_1 = require("./material-topics.service");
let DatapointRequestController = class DatapointRequestController {
    constructor(datapointRequestService, datapointCitationService, datapointDocumentService, datapointStatusService, materialTopicsService, datapointDataRequestSharedService) {
        this.datapointRequestService = datapointRequestService;
        this.datapointCitationService = datapointCitationService;
        this.datapointDocumentService = datapointDocumentService;
        this.datapointStatusService = datapointStatusService;
        this.materialTopicsService = materialTopicsService;
        this.datapointDataRequestSharedService = datapointDataRequestSharedService;
    }
    async getDataRequest(datapointRequestId) {
        return await this.datapointRequestService.findData(datapointRequestId);
    }
    async getMaterialTopics(datapointRequestId) {
        const datapointRequest = await this.datapointRequestService.findData(datapointRequestId);
        return await this.materialTopicsService.loadMaterialTopics(datapointRequest);
    }
    async updateDatapointRequestStatus(datapointRequestId, updateDatapointRequestPayload, req) {
        const userId = req.user.id;
        const workspaceId = req.user.workspaceId;
        const datapointRequest = await this.datapointRequestService.update({
            datapointRequestId,
            updateDatapointRequestPayload,
            userId,
            workspaceId,
            event: 'datapoint_request_status_updated',
        });
        return datapointRequest;
    }
    async updateDatapointRequestResponsiblePerson(datapointRequestId, updatePayload, req) {
        const userId = req.user.id;
        const workspaceId = req.user.workspaceId;
        const datapointRequest = await this.datapointRequestService.update({
            datapointRequestId,
            updateDatapointRequestPayload: {
                responsiblePersonId: updatePayload.responsiblePersonId,
            },
            userId,
            workspaceId,
        });
        return datapointRequest;
    }
    async updateDatapointRequestContent(datapointRequestId, updateDatapointRequestPayload, req) {
        const userId = req.user.id;
        const workspaceId = req.user.workspaceId;
        const datapointRequest = await this.datapointRequestService.update({
            datapointRequestId,
            updateDatapointRequestPayload,
            userId,
            workspaceId,
        });
        return datapointRequest;
    }
    async reviewContentWithAi(datapointRequestId, req) {
        const userId = req.user.id;
        const workspaceId = req.user.workspaceId;
        const datapointRequest = req.datapointRequest;
        return await this.datapointDataRequestSharedService.addDatapointToReviewQueue({
            datapointRequest,
            userId,
            workspaceId,
        });
    }
    async generateContentWithAi(datapointRequestId, additionalData, req) {
        const userId = req.user.id;
        const workspaceId = req.user.workspaceId;
        const datapointRequest = req.datapointRequest;
        if (additionalData.additionalReportTextGenerationRules) {
            await this.datapointRequestService.update({
                datapointRequestId: datapointRequestId,
                updateDatapointRequestPayload: {
                    customUserRemark: additionalData.additionalReportTextGenerationRules,
                },
                userId,
                workspaceId,
                event: 'datapoint_request_custom_user_remark_updated',
            });
        }
        return await this.datapointDataRequestSharedService.addDatapointToGenerationQueue({
            datapointRequest,
            userId,
            workspaceId,
            useExistingReportTextForReference: additionalData.useExistingReportText,
        });
    }
    async getDataRequestCitations(datapointRequestId, { citationId }, req) {
        return await this.datapointCitationService.loadDatapointCitations({
            citationId,
            datapointRequest: req.datapointRequest,
            datapointGeneration: req.datapointGeneration,
        });
    }
    async updateDataRequestCitations(datapointRequestId, payload, req) {
        const userId = req.user.id;
        const workspaceId = req.user.workspaceId;
        const updatedDatapointRequest = await this.datapointCitationService.updateContentAndReplaceCitation({
            datapointRequestId,
            citationId: payload.citationId,
            index: payload.index,
            userId,
            workspaceId,
            datapointRequest: req.datapointRequest,
        });
        return updatedDatapointRequest;
    }
    async updateDatapointGenerationStatus(req, payload) {
        return await this.datapointStatusService.updateGenerationStatus({
            datapointGenerationId: payload.datapointGenerationId,
            status: payload.status,
            userId: req.user.id,
            workspaceId: req.user.workspaceId,
            evaluatorComment: payload.evaluatorComment,
        });
    }
    async assessDatapointScore(payload) {
        return await this.datapointStatusService.assessScore({
            datapointGenerationId: payload.datapointGenerationId,
            textContent: payload.textContent,
        });
    }
    async getDocumentLinksForDatapointRequest(datapointRequestId) {
        return await this.datapointDocumentService.loadDocumentLinks(datapointRequestId);
    }
};
exports.DatapointRequestController = DatapointRequestController;
__decorate([
    (0, common_1.Get)('/:datapointRequestId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific datapoint request by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Datapoint request retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('datapointRequestId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "getDataRequest", null);
__decorate([
    (0, common_1.Get)('/:datapointRequestId/material-topics'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get material topics specific datapoint request by ID',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'All topics related to datapoint fetched successfully',
    }),
    __param(0, (0, common_1.Param)('datapointRequestId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "getMaterialTopics", null);
__decorate([
    (0, permissions_decorator_1.Permissions)(constants_1.SystemPermissions.EDIT_DATAPOINTS),
    (0, common_1.Put)('/:datapointRequestId/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Update datapoint request status' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Datapoint request status updated successfully',
    }),
    __param(0, (0, common_1.Param)('datapointRequestId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "updateDatapointRequestStatus", null);
__decorate([
    (0, permissions_decorator_1.Permissions)(constants_1.SystemPermissions.ASSIGN_USERS_DATAPOINTS),
    (0, common_1.Put)('/:datapointRequestId/responsible-person'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update responsible person for a datapoint request',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Datapoint request responsible person updated successfully',
    }),
    __param(0, (0, common_1.Param)('datapointRequestId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "updateDatapointRequestResponsiblePerson", null);
__decorate([
    (0, permissions_decorator_1.Permissions)(constants_1.SystemPermissions.EDIT_DATAPOINTS),
    (0, common_1.Put)('/:datapointRequestId/content'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a specific datapoint request by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Datapoint request updated successfully',
    }),
    __param(0, (0, common_1.Param)('datapointRequestId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "updateDatapointRequestContent", null);
__decorate([
    (0, permissions_decorator_1.Permissions)(constants_1.SystemPermissions.GAP_ANALYSIS_DATAPOINTS),
    (0, common_1.Post)('/:datapointRequestId/review-with-ai'),
    (0, swagger_1.ApiOperation)({ summary: 'Review datapoint request content with AI' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Datapoint request content reviewed successfully',
    }),
    __param(0, (0, common_1.Param)('datapointRequestId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "reviewContentWithAi", null);
__decorate([
    (0, permissions_decorator_1.Permissions)(constants_1.SystemPermissions.GAP_ANALYSIS_DATAPOINTS),
    (0, common_1.Post)('/:datapointRequestId/generate-with-ai'),
    (0, swagger_1.ApiOperation)({ summary: 'Generate datapoint request content with AI' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Datapoint request content generated successfully',
    }),
    __param(0, (0, common_1.Param)('datapointRequestId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Function, Object]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "generateContentWithAi", null);
__decorate([
    (0, common_1.Get)('/:datapointRequestId/citations'),
    (0, swagger_1.ApiOperation)({ summary: 'Get citations specific datapoint request by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
    }),
    __param(0, (0, common_1.Param)('datapointRequestId')),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "getDataRequestCitations", null);
__decorate([
    (0, common_1.Put)('/:datapointRequestId/citations'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update citations specific datapoint request by ID',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Datapoint request citations updated successfully',
    }),
    __param(0, (0, common_1.Param)('datapointRequestId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "updateDataRequestCitations", null);
__decorate([
    (0, permissions_decorator_1.Permissions)(constants_1.SystemPermissions.GAP_ANALYSIS_DATAPOINTS_REVIEW),
    (0, common_1.Put)('/:datapointRequestId/generation-status'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update generation status specific datapoint request by ID',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Datapoint request generation status updated successfully',
    }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "updateDatapointGenerationStatus", null);
__decorate([
    (0, permissions_decorator_1.Permissions)(constants_1.SystemPermissions.GAP_ANALYSIS_DATAPOINTS_REVIEW),
    (0, common_1.Post)('/:datapointRequestId/assess-score'),
    (0, swagger_1.ApiOperation)({
        summary: 'Assess score for datapoint generation using external evaluation API',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Datapoint generation score assessed successfully',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "assessDatapointScore", null);
__decorate([
    (0, common_1.Get)('/:datapointRequestId/document-links'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get document links specific datapoint request by ID',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Datapoint request document links fetched successfully',
    }),
    __param(0, (0, common_1.Param)('datapointRequestId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DatapointRequestController.prototype, "getDocumentLinksForDatapointRequest", null);
exports.DatapointRequestController = DatapointRequestController = __decorate([
    (0, swagger_1.ApiTags)('Datapoint Request'),
    (0, common_2.UseGuards)(auth_guard_1.AuthGuard),
    (0, common_2.UseGuards)(permission_guard_1.PermissionGuard),
    (0, common_2.UseGuards)(datapoint_request_guard_1.DatapointRequestGuard),
    (0, common_1.Controller)('datapoint-request'),
    __metadata("design:paramtypes", [datapoint_request_service_1.DatapointRequestService,
        datapoint_citation_service_1.DatapointCitationService,
        datapoint_document_service_1.DatapointDocumentService,
        datapoint_status_service_1.DatapointStatusService,
        material_topics_service_1.MaterialTopicsService,
        shared_datapoint_datarequest_service_1.DatapointDataRequestSharedService])
], DatapointRequestController);
//# sourceMappingURL=datapoint-request.controller.js.map