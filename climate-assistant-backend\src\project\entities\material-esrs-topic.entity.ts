import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  ManyToOne,
  <PERSON>in<PERSON>ol<PERSON>n,
  CreateDateColumn,
  Column,
  Index,
} from 'typeorm';
import { Project } from './project.entity';
import { ESRSTopic } from '../../esrs/entities/esrs-topic.entity';

@Entity()
export class MaterialESRSTopic {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  projectId: string;

  @ManyToOne(() => Project, (project) => project.materialTopics)
  @JoinColumn({ name: 'projectId' })
  project: Project;

  @Column()
  @Index()
  esrsTopicId: number;

  @ManyToOne(() => ESRSTopic)
  @JoinColumn({ name: 'esrsTopicId' })
  esrsTopic: ESRSTopic;

  @Column({ default: true })
  active: boolean;

  @CreateDateColumn()
  createdAt: Date;
}
