import { Language, Project } from './project.entity';
import { Workspace } from 'src/workspace/entities/workspace.entity';
import { User } from 'src/users/entities/user.entity';
import { MaterialESRSTopic } from './material-esrs-topic.entity';
import { DataRequest } from 'src/data-request/entities/data-request.entity';
import { ESRSDisclosureRequirement } from 'src/esrs/entities/esrs-disclosure-requirement.entity';

export class CreateProjectRequest {
  name: string;
  primaryContentLanguage: Language;
  type?: string;
  reportingYear?: string;
}

export class UpdateProjectRequest {
  name: string;
  reportTextGenerationRules: string;
  primaryContentLanguage: Language;
  reportingYear?: string;
}

export class DataRequestData extends DataRequest {
  responsiblePerson: User;
  approver: User;
  disclosureRequirement: ESRSDisclosureRequirement;
}

export class ProjectData extends Project {
  workspace: Workspace;
  creator: User;
  materialTopics: MaterialESRSTopic[];
  dataRequests: DataRequestData[];
}
