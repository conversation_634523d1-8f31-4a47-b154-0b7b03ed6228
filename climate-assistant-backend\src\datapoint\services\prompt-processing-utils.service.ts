import { Injectable } from '@nestjs/common';
import { ESRSDatapoint } from 'src/datapoint/entities/esrs-datapoint.entity';
import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { ESRSTopic } from 'src/esrs/entities/esrs-topic.entity';
import { Company } from 'src/workspace/entities/company.entity';
import { LLM_MODELS } from 'src/constants';
import { ChatCompletionMessageParam } from 'openai/resources';
import { TimingTracker } from 'src/util/timing-util';
import { DatapointTypeHelper } from '../utils/datapoint-type.helper';
import { WorkerLogger } from 'src/shared/logger.service';

export interface AIProcessingOptions {
  datapointRequestId: string;
  userId: string;
  workspaceId: string;
  dryRun?: boolean;
  testPrompts?: Record<
    string,
    { prompt: string; chainIdentifier: string; model: LLM_MODELS }
  >;
  useExistingReportTextForReference?: boolean;
}

export interface UnifiedPromptVariables {
  esrsDatapoint: {
    datapointId: string;
    name: string;
    lawText: string;
    footnotes?: string;
    lawTextAR?: string;
    footnotesAR?: string;
    esrsDisclosureRequirement: {
      dr: string;
    };
    companyName: string;
    isConditional: boolean;
  };
  language: string;
  reportTextGenerationRules: string;
  generalCompanyProfile: any;
  reportingYear: string;
  currentYear: number;
  hasMaterialTopics: boolean;
  materialTopics?: string;
  mainMaterialTopics?: string;
  hasNonMaterialTopics: boolean;
  nonMaterialTopics?: string;
  [key: string]: any;
}

export interface PromptBuildResult {
  prompts: ChatCompletionMessageParam[];
  promptIds: string[];
}

@Injectable()
export class PromptProcessingUtilsService {
  constructor(private readonly datapointTypeHelper: DatapointTypeHelper) {}

  private readonly logger = new WorkerLogger(PromptProcessingUtilsService.name);

  /**
   * Determines the feature type based on datapoint characteristics
   */
  determineFeatureType(
    esrsDatapoint: ESRSDatapoint,
    content: string,
    processType: 'generation' | 'gap-analysis'
  ): string {
    const h2Count = (content.match(/<h2>/g) || []).length;
    const isNumeric = this.datapointTypeHelper.isNumericDataPoint(
      esrsDatapoint.dataType
    );
    const isTabular = esrsDatapoint.dataType?.includes('table');

    if (processType === 'gap-analysis') {
      if (esrsDatapoint.datapointId.includes('MDR') && h2Count > 0) {
        return 'DP_GAP_ANALYSIS_MDR';
      }
      return 'DP_GAP_ANALYSIS_DEFAULT';
    }

    // Generation features
    if (esrsDatapoint.datapointId.includes('MDR-A') && h2Count > 0) {
      return 'DP_MDRA_GENERATION';
    } else if (esrsDatapoint.datapointId.includes('MDR-P') && h2Count > 0) {
      return 'DP_MDRP_GENERATION';
    } else if (esrsDatapoint.datapointId.includes('MDR-T') && h2Count > 0) {
      return 'DP_MDRT_GENERATION';
    } else if (isTabular) {
      return 'DP_TABLE_GENERATION';
    } else if (isNumeric) {
      return 'DP_NUMERIC_GENERATION';
    }

    return 'DP_DEFAULT_GENERATION';
  }

  /**
   * Builds unified variables for prompt compilation
   */
  buildUnifiedVariables(
    datapointRequest: DatapointRequest,
    esrsDatapoint: ESRSDatapoint,
    materialTopicsInHierarchy: ESRSTopic[],
    nonMaterialTopicsInHierarchy: ESRSTopic[],
    generalCompanyProfile: string,
    companyDetails?: Company,
    additionalVariables?: Record<string, any>
  ): UnifiedPromptVariables {
    const baseVariables: UnifiedPromptVariables = {
      esrsDatapoint: {
        datapointId: esrsDatapoint.datapointId,
        name: esrsDatapoint.name,
        lawText: esrsDatapoint.lawText,
        footnotes: esrsDatapoint.footnotes,
        lawTextAR: esrsDatapoint.lawTextAR,
        footnotesAR: esrsDatapoint.footnotesAR,
        esrsDisclosureRequirement: esrsDatapoint.esrsDisclosureRequirement
          ? {
              dr: esrsDatapoint.esrsDisclosureRequirement.dr,
            }
          : undefined,
        companyName: companyDetails?.name || 'The company',
        isConditional: esrsDatapoint.conditional || false,
      },
      language: datapointRequest.dataRequest.project.primaryContentLanguage,
      reportTextGenerationRules:
        datapointRequest.dataRequest.project.reportTextGenerationRules || '',
      generalCompanyProfile: generalCompanyProfile || {},
      reportingYear: datapointRequest.dataRequest.project.reportingYear || '',
      currentYear: new Date().getFullYear(),
      hasMaterialTopics: materialTopicsInHierarchy.length > 0,
      hasNonMaterialTopics: nonMaterialTopicsInHierarchy.length > 0,
    };

    // Add material topics
    if (materialTopicsInHierarchy.length > 0) {
      // Note: This would need to use the formatTopics method from PromptService
      // For now, we'll create a simple format
      const mainMaterialTopics = materialTopicsInHierarchy
        .filter((t) => !t.parentId)
        .map((t) => t.name);
      baseVariables.materialTopics = materialTopicsInHierarchy
        .map((t) => `- ${t.name}`)
        .join('\n');
      baseVariables.mainMaterialTopics = mainMaterialTopics.join(', ');
    }

    // Add non-material topics
    if (nonMaterialTopicsInHierarchy.length > 0) {
      baseVariables.nonMaterialTopics = nonMaterialTopicsInHierarchy
        .map((t) => `- ${t.name}`)
        .join('\n');
    }

    // Merge additional variables
    return { ...baseVariables, ...additionalVariables };
  }

  /**
   * Determines the appropriate model based on context
   * Gets model from the first prompt in chain (A1, B1, C1, etc.)
   */
  determineModel(
    testPrompts?: Record<
      string,
      { prompt: string; chainIdentifier: string; model: LLM_MODELS }
    >,
    dryRun?: boolean
  ): LLM_MODELS {
    if (dryRun && testPrompts) {
      // Find the first prompt in chain order (A1, B1, C1, etc.)
      const sortedPrompts = Object.entries(testPrompts).sort(([, a], [, b]) =>
        a.chainIdentifier.localeCompare(b.chainIdentifier)
      );
      const firstPrompt = sortedPrompts[0]?.[1];
      return firstPrompt?.model || LLM_MODELS.o3;
    }
    return LLM_MODELS.o3;
  }

  /**
   * Gets model from database prompt for a specific feature and chain
   */
  async getModelFromDatabase(
    promptManagementService: any,
    feature: string,
    chainIdentifier: string = 'A1'
  ): Promise<LLM_MODELS> {
    try {
      const prompt = await promptManagementService.findByFeatureAndChain(
        feature,
        chainIdentifier
      );
      return prompt?.model || LLM_MODELS.o3;
    } catch (error) {
      this.logger.warn(
        `Could not fetch model from DB for feature ${feature}, chain ${chainIdentifier}, using default o3`
      );
      return LLM_MODELS.o3;
    }
  }

  /**
   * Unified model selection for any chain identifier (A1, B1, C1, etc.)
   * Handles both dry run with test prompts and production with database prompts
   */
  async getModelForChain(
    promptManagementService: any,
    feature: string,
    chainIdentifier: string,
    testPrompts?: Record<
      string,
      { prompt: string; chainIdentifier: string; model: LLM_MODELS }
    >,
    dryRun?: boolean,
    fallbackModel?: LLM_MODELS
  ): Promise<LLM_MODELS> {
    if (dryRun && testPrompts && testPrompts[chainIdentifier]) {
      // In dry run mode with test prompts, get model from specific chain
      return (
        testPrompts[chainIdentifier].model || fallbackModel || LLM_MODELS.o3
      );
    } else if (dryRun && testPrompts) {
      // In dry run mode but no specific chain, use first prompt model
      return this.determineModel(testPrompts, dryRun);
    } else {
      // In normal mode, get model from database for the specific chain
      return await this.getModelFromDatabase(
        promptManagementService,
        feature,
        chainIdentifier
      );
    }
  }

  /**
   * Extracts timing information if available
   */
  extractTimings(timingTracker?: TimingTracker): any {
    return {
      processes: timingTracker?.getTimings() || [],
      totalDuration: timingTracker?.getTotalDuration() || 0,
    };
  }

  /**
   * Logs feature determination
   */
  logFeatureType(
    feature: string,
    esrsDatapoint: ESRSDatapoint,
    h2Count?: number
  ): void {
    if (feature.includes('MDR') && h2Count !== undefined) {
      this.logger.log(
        `${feature} identified: ${esrsDatapoint.datapointId} with ${h2Count} policies`
      );
    } else {
      this.logger.log(`${feature} identified: ${esrsDatapoint.datapointId}`);
    }
  }
}
