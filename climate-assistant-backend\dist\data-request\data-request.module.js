"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataRequestModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const data_request_service_1 = require("./data-request.service");
const workspace_entity_1 = require("../workspace/entities/workspace.entity");
const data_request_guard_1 = require("./data-request.guard");
const data_request_controller_1 = require("./data-request.controller");
const data_request_entity_1 = require("./entities/data-request.entity");
const datapoint_request_entity_1 = require("../datapoint/entities/datapoint-request.entity");
const prompts_module_1 = require("../prompts/prompts.module");
const users_module_1 = require("../users/users.module");
const workspace_module_1 = require("../workspace/workspace.module");
const datapoint_request_module_1 = require("../datapoint/datapoint-request.module");
const bull_1 = require("@nestjs/bull");
const datarequest_generation_entity_1 = require("./entities/datarequest-generation.entity");
const shared_module_1 = require("../shared/shared.module");
const esrs_disclosure_requirement_entity_1 = require("../esrs/entities/esrs-disclosure-requirement.entity");
const esrs_datapoint_entity_1 = require("../datapoint/entities/esrs-datapoint.entity");
const user_entity_1 = require("../users/entities/user.entity");
const user_prompt_context_entity_1 = require("../users/entities/user-prompt-context.entity");
const version_history_entity_1 = require("../workspace/entities/version-history.entity");
const llm_module_1 = require("../llm/llm.module");
const project_module_1 = require("../project/project.module");
const document_module_1 = require("../document/document.module");
const datapoint_document_chunk_module_1 = require("../datapoint-document-chunk/datapoint-document-chunk.module");
const esrs_module_1 = require("../esrs/esrs.module");
const jobs_1 = require("../types/jobs");
const queue_config_1 = require("../util/queue.config");
let DataRequestModule = class DataRequestModule {
};
exports.DataRequestModule = DataRequestModule;
exports.DataRequestModule = DataRequestModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                data_request_entity_1.DataRequest,
                esrs_disclosure_requirement_entity_1.ESRSDisclosureRequirement,
                esrs_datapoint_entity_1.ESRSDatapoint,
                workspace_entity_1.Workspace,
                user_entity_1.User,
                user_prompt_context_entity_1.UserPromptContext,
                version_history_entity_1.VersionHistory,
                datapoint_request_entity_1.DatapointRequest,
                datarequest_generation_entity_1.DataRequestGeneration,
            ]),
            (0, common_1.forwardRef)(() => datapoint_request_module_1.DatapointRequestModule),
            users_module_1.UsersModule,
            prompts_module_1.PromptModule,
            workspace_module_1.WorkspaceModule,
            llm_module_1.LlmModule,
            llm_module_1.LlmModule,
            shared_module_1.SharedModule,
            (0, common_1.forwardRef)(() => project_module_1.ProjectModule),
            bull_1.BullModule.registerQueue({
                name: jobs_1.JobProcessor.DatapointGeneration,
                defaultJobOptions: queue_config_1.DEFAULT_QUEUE_JOB_OPTIONS,
            }),
            bull_1.BullModule.registerQueue({
                name: jobs_1.JobProcessor.DatapointReview,
                defaultJobOptions: queue_config_1.DEFAULT_QUEUE_JOB_OPTIONS,
            }),
            (0, common_1.forwardRef)(() => datapoint_request_module_1.DatapointRequestModule),
            document_module_1.DocumentModule,
            datapoint_document_chunk_module_1.DatapointDocumentChunkModule,
            esrs_module_1.EsrsModule,
        ],
        providers: [data_request_service_1.DataRequestService, data_request_guard_1.DataRequestGuard],
        exports: [data_request_service_1.DataRequestService],
        controllers: [data_request_controller_1.DataRequestController],
    })
], DataRequestModule);
//# sourceMappingURL=data-request.module.js.map