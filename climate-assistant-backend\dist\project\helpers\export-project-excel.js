"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateExcelReport = generateExcelReport;
exports.convertHtmlToPlainText = convertHtmlToPlainText;
const ExcelJS = require("exceljs");
const marked_1 = require("marked");
const cheerio = require("cheerio");
const llm_response_util_1 = require("../../util/llm-response-util");
const spanRegex = /<span[^>]*>(.*?)<\/span>/g;
async function generateExcelReport(datapoints) {
    try {
        const workbook = new ExcelJS.Workbook();
        workbook.creator = 'ESG Data Tool';
        workbook.lastModifiedBy = 'ESG Data Tool';
        workbook.created = new Date();
        workbook.modified = new Date();
        const worksheet = workbook.addWorksheet('Datapoints Report', {
            views: [{ state: 'frozen', xSplit: 0, ySplit: 1 }],
        });
        worksheet.columns = [
            { header: 'ESRS Topic', key: 'esrs', width: 10 },
            { header: 'Disclosure Requirement ID', key: 'dr', width: 15 },
            { header: 'Datapoint ID', key: 'datapointId', width: 15 },
            { header: 'Paragraph', key: 'paragraph', width: 12 },
            { header: 'AR', key: 'ar', width: 12 },
            { header: 'Title', key: 'datapoint', width: 30 },
            { header: 'Data Type', key: 'dataType', width: 15 },
            { header: 'Datapoint Text', key: 'datapointText', width: 40 },
            { header: 'Clarification of the gap', key: 'gapExplanation', width: 40 },
            { header: 'Recommended Actions', key: 'recommendedActions', width: 40 },
            { header: 'Example Text', key: 'exampleText', width: 40 },
            { header: 'Disclaimer', key: 'disclaimer', width: 40 },
        ];
        const headerRow = worksheet.getRow(1);
        headerRow.height = 25;
        headerRow.font = { bold: true, size: 12 };
        headerRow.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFD3D3D3' },
        };
        headerRow.alignment = { vertical: 'middle', horizontal: 'center' };
        headerRow.eachCell((cell) => {
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
        });
        for (let i = 0; i < datapoints.length; i++) {
            const datapoint = datapoints[i];
            let datapointTextContent = '';
            if (datapoint.datapointText && datapoint.datapointText !== '') {
                try {
                    let cleanedText = datapoint.datapointText
                        .replace(llm_response_util_1.CITATION_CLIENT_REGEX, '$3')
                        .replace(llm_response_util_1.MERGED_CELL_REGEX, '')
                        .trim();
                    const htmlContent = '<meta charset="UTF-8">' + (await (0, marked_1.marked)(cleanedText));
                    datapointTextContent = convertHtmlToPlainText(htmlContent);
                }
                catch (error) {
                    console.error('Error processing datapointText:', error);
                    datapointTextContent = datapoint.datapointText;
                }
            }
            let gapExplanation = '';
            let recommendedActions = '';
            let exampleText = '';
            let disclaimer = '';
            if (datapoint.datapointGaps && datapoint.datapointGaps !== '') {
                try {
                    gapExplanation = extractGapExplanation(datapoint.datapointGaps);
                    recommendedActions = extractRecommendedActions(datapoint.datapointGaps);
                    exampleText = extractExampleText(datapoint.datapointGaps);
                    disclaimer = extractDisclaimer(datapoint.datapointGaps);
                }
                catch (error) {
                    console.error('Error extracting gap information:', error);
                }
            }
            const row = worksheet.addRow({
                esrs: datapoint.esrs || '',
                dr: datapoint.dr || '',
                datapointId: datapoint.datapointId || '',
                paragraph: datapoint.paragraph || '',
                ar: datapoint.ar || '',
                datapoint: datapoint.datapoint || '',
                dataType: datapoint.dataType || '',
                datapointText: datapointTextContent,
                gapExplanation,
                recommendedActions,
                exampleText,
                disclaimer,
            });
            row.height = 120;
            row.eachCell((cell, colNumber) => {
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' },
                };
                cell.alignment = {
                    wrapText: true,
                    vertical: 'top',
                };
                let INFO_COLUMNS = 8;
                if (colNumber === INFO_COLUMNS + 1) {
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FFFFF2CC' },
                    };
                }
                else if (colNumber === INFO_COLUMNS + 2) {
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FFD9E1F2' },
                    };
                }
                else if (colNumber === INFO_COLUMNS + 3) {
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FFE2EFDA' },
                    };
                }
                else if (colNumber === INFO_COLUMNS + 4) {
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FFFCE4D6' },
                    };
                }
            });
        }
        const buffer = await workbook.xlsx.writeBuffer();
        return Buffer.from(buffer);
    }
    catch (error) {
        console.error('Error generating Excel report:', error);
        throw new Error(`Failed to generate Excel report: ${error.message}`);
    }
}
function convertHtmlToPlainText(html) {
    try {
        const $ = cheerio.load(html);
        $('li').each((_, el) => {
            $(el).prepend('• ');
        });
        $('p, li').each((_, el) => {
            $(el).append('\n');
        });
        let text = $.text();
        text = text
            .replace(/\n\s+/g, '\n')
            .replace(/\n+/g, '\n')
            .trim();
        return text;
    }
    catch (error) {
        console.error('Error converting HTML to plain text:', error);
        return html.replace(/<[^>]*>/g, '').trim();
    }
}
function extractGapExplanation(html) {
    try {
        const $ = cheerio.load(html);
        const gapP = $('p:contains("Gap Identified:") p');
        if (gapP.length > 0) {
            return gapP.text().trim();
        }
        const regex = /<strong>Gap Identified:<\/strong>.*?<p>(.*?)<\/p>/s;
        const match = html.match(regex);
        const cleanedGap = match
            ? match[1].replace(spanRegex, (match, p1) => p1)
            : '';
        return cleanedGap.trim();
    }
    catch (error) {
        console.error('Error extracting gap explanation:', error);
        return '';
    }
}
function extractRecommendedActions(html) {
    try {
        const $ = cheerio.load(html);
        const actionItems = $('p:contains("Recommended Actions:") + ul li p');
        if (actionItems.length > 0) {
            const items = actionItems
                .map((_, el) => {
                return `• ${$(el).text().trim()}`;
            })
                .get();
            const actions = items.join('\n');
            return actions.trim();
        }
        const recommendedHeader = $('p strong:contains("Recommended Actions:")').closest('p');
        const actionList = recommendedHeader.nextAll('ul').first();
        if (actionList.length > 0) {
            const items = actionList
                .find('li p')
                .map((_, el) => {
                return `• ${$(el).text().trim()}`;
            })
                .get();
            return items.join('\n');
        }
        const regex = /<strong>Recommended Actions:<\/strong><\/p><ul>(.*?)<\/ul>/s;
        const match = html.match(regex);
        if (!match)
            return '';
        const listItemRegex = /<li><p>(.*?)<\/p><\/li>/g;
        const items = [];
        let listItemMatch;
        while ((listItemMatch = listItemRegex.exec(match[1])) !== null) {
            items.push(`• ${listItemMatch[1].trim()}`);
        }
        const actions = items.join('\n');
        const cleanedActions = actions
            ? actions[1].replace(spanRegex, (match, p1) => p1)
            : '';
        return cleanedActions.trim();
    }
    catch (error) {
        console.error('Error extracting recommended actions:', error);
        return '';
    }
}
function extractExampleText(html) {
    try {
        const $ = cheerio.load(html);
        const exampleP = $('p:contains("Example Text:") p');
        if (exampleP.length > 0) {
            return exampleP.text().trim();
        }
        const regex = /<strong>Example Text:<\/strong><p>(.*?)<\/p>/s;
        const match = html.match(regex);
        const cleanedText = match
            ? match[1].replace(spanRegex, (match, p1) => p1)
            : '';
        return cleanedText.trim();
    }
    catch (error) {
        console.error('Error extracting example text:', error);
        return '';
    }
}
function extractDisclaimer(html) {
    try {
        const $ = cheerio.load(html);
        const disclaimerP = $('p:contains("Disclaimer:") p');
        if (disclaimerP.length > 0) {
            return disclaimerP.text().trim();
        }
        const regex = /<strong>Disclaimer:<\/strong>\s*<p>(.*?)<\/p>/s;
        const match = html.match(regex);
        const cleanedDisclaimer = match
            ? match[1].replace(spanRegex, (match, p1) => p1)
            : '';
        return cleanedDisclaimer.trim();
    }
    catch (error) {
        console.error('Error extracting disclaimer:', error);
        return '';
    }
}
//# sourceMappingURL=export-project-excel.js.map