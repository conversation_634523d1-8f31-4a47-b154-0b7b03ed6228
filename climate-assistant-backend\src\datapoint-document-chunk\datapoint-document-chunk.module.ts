import { Module } from '@nestjs/common';
import { DatapointDocumentChunkService } from './datapoint-document-chunk.service';
import { DatapointDocumentChunk } from './entities/datapoint-document-chunk.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PromptService } from 'src/prompts/prompts.service';
import { DocumentChunk } from 'src/document/entities/document-chunk.entity';
import { Document } from 'src/document/entities/document.entity';
import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { ESRSDisclosureRequirement } from 'src/esrs/entities/esrs-disclosure-requirement.entity';
import { User } from 'src/users/entities/user.entity';
import { UsersModule } from 'src/users/users.module';
import { LlmModule } from 'src/llm/llm.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      DatapointDocumentChunk,
      DocumentChunk,
      Document,
      DatapointRequest,
      ESRSDisclosureRequirement,
      User,
    ]),
    UsersModule,
    LlmModule,
  ],
  providers: [DatapointDocumentChunkService, PromptService],
  exports: [DatapointDocumentChunkModule, DatapointDocumentChunkService],
  controllers: [],
})
export class DatapointDocumentChunkModule {}
