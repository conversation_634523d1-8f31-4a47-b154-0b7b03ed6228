"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddDatapointAssignment1753858134931 = void 0;
const constants_1 = require("../../constants");
class AddDatapointAssignment1753858134931 {
    constructor() {
        this.name = 'AddDatapointAssignmentPermissions1753858134931';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "datapoint_request" ADD COLUMN IF NOT EXISTS "responsiblePersonId" uuid`);
        await queryRunner.query(`ALTER TABLE "datapoint_request" ADD CONSTRAINT "FK_744a3ccc48b6d54cae19418bed8" FOREIGN KEY ("responsiblePersonId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`
          INSERT INTO "permission" ("name", "description")
          VALUES ($1, $2)
          ON CONFLICT ("name") DO NOTHING
          `, [constants_1.SystemPermissions.ASSIGN_USERS_DATAPOINTS, 'Assign users to datapoints']);
        await queryRunner.query(`
          WITH role_data AS (
            SELECT id FROM "role" WHERE name IN ('WORKSPACE_ADMIN', 'SUPER_ADMIN')
          ),
          permission_data AS (
            SELECT id FROM "permission" WHERE name = $1
          )
          INSERT INTO "role_permission" ("roleId", "permissionId")
          SELECT rd.id, pd.id
          FROM role_data rd
          CROSS JOIN permission_data pd
          ON CONFLICT ("roleId", "permissionId") DO NOTHING
          `, [constants_1.SystemPermissions.ASSIGN_USERS_DATAPOINTS]);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      DELETE FROM "role_permission"
      WHERE "roleId" IN (SELECT id FROM "role" WHERE name IN ('WORKSPACE_ADMIN', 'SUPER_ADMIN'))
      AND "permissionId" IN (SELECT id FROM "permission" WHERE name = $1)
      `, [constants_1.SystemPermissions.ASSIGN_USERS_DATAPOINTS]);
        await queryRunner.query(`
      DELETE FROM "permission"
      WHERE name = $1
      AND NOT EXISTS (
        SELECT 1 FROM "role_permission" rp
        WHERE rp."permissionId" = "permission".id
      )
      `, [constants_1.SystemPermissions.ASSIGN_USERS_DATAPOINTS]);
        await queryRunner.query(`ALTER TABLE "datapoint_request" DROP CONSTRAINT "FK_744a3ccc48b6d54cae19418bed8"`);
        await queryRunner.query(`ALTER TABLE "datapoint_request" DROP COLUMN "responsiblePersonId"`);
    }
}
exports.AddDatapointAssignment1753858134931 = AddDatapointAssignment1753858134931;
//# sourceMappingURL=1753858134931-schema-update.js.map