"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatapointRequestModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const datapoint_request_service_1 = require("./datapoint-request.service");
const workspace_entity_1 = require("../workspace/entities/workspace.entity");
const datapoint_request_guard_1 = require("./datapoint-request.guard");
const datapoint_request_controller_1 = require("./datapoint-request.controller");
const datapoint_request_entity_1 = require("./entities/datapoint-request.entity");
const esrs_datapoint_entity_1 = require("./entities/esrs-datapoint.entity");
const datapoint_document_chunk_entity_1 = require("../datapoint-document-chunk/entities/datapoint-document-chunk.entity");
const data_request_module_1 = require("../data-request/data-request.module");
const prompts_module_1 = require("../prompts/prompts.module");
const project_module_1 = require("../project/project.module");
const users_module_1 = require("../users/users.module");
const workspace_module_1 = require("../workspace/workspace.module");
const datapoint_generation_entity_1 = require("./entities/datapoint-generation.entity");
const esrs_topic_datapoint_entity_1 = require("../esrs/entities/esrs-topic-datapoint.entity");
const shared_module_1 = require("../shared/shared.module");
const jobs_1 = require("../types/jobs");
const bull_1 = require("@nestjs/bull");
const document_module_1 = require("../document/document.module");
const prompt_management_module_1 = require("../prompt-management/prompt-management.module");
const queue_config_1 = require("../util/queue.config");
const datapoint_citation_service_1 = require("./datapoint-citation.service");
const datapoint_status_service_1 = require("./datapoint-status.service");
const datapoint_generation_service_1 = require("./datapoint-generation.service");
const datapoint_document_service_1 = require("./datapoint-document.service");
const material_topics_service_1 = require("./material-topics.service");
const datapoint_type_helper_1 = require("./utils/datapoint-type.helper");
const prompt_processing_utils_service_1 = require("./services/prompt-processing-utils.service");
const llm_module_1 = require("../llm/llm.module");
let DatapointRequestModule = class DatapointRequestModule {
};
exports.DatapointRequestModule = DatapointRequestModule;
exports.DatapointRequestModule = DatapointRequestModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                esrs_datapoint_entity_1.ESRSDatapoint,
                esrs_topic_datapoint_entity_1.ESRSTopicDatapoint,
                datapoint_request_entity_1.DatapointRequest,
                workspace_entity_1.Workspace,
                datapoint_document_chunk_entity_1.DatapointDocumentChunk,
                datapoint_generation_entity_1.DatapointGeneration,
            ]),
            (0, common_1.forwardRef)(() => data_request_module_1.DataRequestModule),
            llm_module_1.LlmModule,
            prompts_module_1.PromptModule,
            prompt_management_module_1.PromptManagementModule,
            project_module_1.ProjectModule,
            users_module_1.UsersModule,
            workspace_module_1.WorkspaceModule,
            shared_module_1.SharedModule,
            bull_1.BullModule.registerQueue({
                name: jobs_1.JobProcessor.DatapointGeneration,
                defaultJobOptions: queue_config_1.DEFAULT_QUEUE_JOB_OPTIONS,
            }),
            bull_1.BullModule.registerQueue({
                name: jobs_1.JobProcessor.DatapointReview,
                defaultJobOptions: queue_config_1.DEFAULT_QUEUE_JOB_OPTIONS,
            }),
            data_request_module_1.DataRequestModule,
            document_module_1.DocumentModule,
        ],
        providers: [
            datapoint_request_service_1.DatapointRequestService,
            datapoint_request_guard_1.DatapointRequestGuard,
            datapoint_citation_service_1.DatapointCitationService,
            datapoint_status_service_1.DatapointStatusService,
            datapoint_generation_service_1.DatapointGenerationService,
            datapoint_document_service_1.DatapointDocumentService,
            material_topics_service_1.MaterialTopicsService,
            datapoint_type_helper_1.DatapointTypeHelper,
            prompt_processing_utils_service_1.PromptProcessingUtilsService,
        ],
        exports: [
            datapoint_request_service_1.DatapointRequestService,
            datapoint_citation_service_1.DatapointCitationService,
            datapoint_status_service_1.DatapointStatusService,
            datapoint_generation_service_1.DatapointGenerationService,
            datapoint_document_service_1.DatapointDocumentService,
            material_topics_service_1.MaterialTopicsService,
            datapoint_type_helper_1.DatapointTypeHelper,
            prompt_processing_utils_service_1.PromptProcessingUtilsService,
        ],
        controllers: [datapoint_request_controller_1.DatapointRequestController],
    })
], DatapointRequestModule);
//# sourceMappingURL=datapoint-request.module.js.map