"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ESRSDatapoint = void 0;
const esrs_topic_datapoint_entity_1 = require("../../esrs/entities/esrs-topic-datapoint.entity");
const esrs_disclosure_requirement_entity_1 = require("../../esrs/entities/esrs-disclosure-requirement.entity");
const typeorm_1 = require("typeorm");
let ESRSDatapoint = class ESRSDatapoint {
};
exports.ESRSDatapoint = ESRSDatapoint;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], ESRSDatapoint.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar' }),
    __metadata("design:type", String)
], ESRSDatapoint.prototype, "datapointId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar' }),
    __metadata("design:type", String)
], ESRSDatapoint.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], ESRSDatapoint.prototype, "optional", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], ESRSDatapoint.prototype, "conditional", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ESRSDatapoint.prototype, "paragraph", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ESRSDatapoint.prototype, "relatedAR", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ESRSDatapoint.prototype, "lawText", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ESRSDatapoint.prototype, "lawTextAR", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ESRSDatapoint.prototype, "footnotes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ESRSDatapoint.prototype, "footnotesAR", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], ESRSDatapoint.prototype, "dataType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], ESRSDatapoint.prototype, "publicAccess", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ESRSDatapoint.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], ESRSDatapoint.prototype, "esrsDisclosureRequirementId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ESRSDatapoint.prototype, "exampleOutput", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ESRSDatapoint.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => esrs_disclosure_requirement_entity_1.ESRSDisclosureRequirement, (disclosureRequirement) => disclosureRequirement.esrsDatapoints),
    (0, typeorm_1.JoinColumn)({ name: 'esrsDisclosureRequirementId' }),
    __metadata("design:type", esrs_disclosure_requirement_entity_1.ESRSDisclosureRequirement)
], ESRSDatapoint.prototype, "esrsDisclosureRequirement", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => esrs_topic_datapoint_entity_1.ESRSTopicDatapoint, (relation) => relation.datapoint),
    __metadata("design:type", Array)
], ESRSDatapoint.prototype, "topicRelations", void 0);
exports.ESRSDatapoint = ESRSDatapoint = __decorate([
    (0, typeorm_1.Entity)()
], ESRSDatapoint);
//# sourceMappingURL=esrs-datapoint.entity.js.map