"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataRequest = exports.DataRequestQueueStatus = exports.DataRequestStatus = void 0;
const esrs_disclosure_requirement_entity_1 = require("../../esrs/entities/esrs-disclosure-requirement.entity");
const user_entity_1 = require("../../users/entities/user.entity");
const typeorm_1 = require("typeorm");
const project_entity_1 = require("../../project/entities/project.entity");
const datapoint_request_entity_1 = require("../../datapoint/entities/datapoint-request.entity");
const comment_entity_1 = require("../../project/entities/comment.entity");
const comment_generation_entity_1 = require("../../project/entities/comment-generation.entity");
const datarequest_generation_entity_1 = require("./datarequest-generation.entity");
var DataRequestStatus;
(function (DataRequestStatus) {
    DataRequestStatus["IncompleteData"] = "incomplete_data";
    DataRequestStatus["CompleteData"] = "complete_data";
    DataRequestStatus["NotReported"] = "not_reported";
})(DataRequestStatus || (exports.DataRequestStatus = DataRequestStatus = {}));
var DataRequestQueueStatus;
(function (DataRequestQueueStatus) {
    DataRequestQueueStatus["QueuedForGeneration"] = "queued_for_generation";
    DataRequestQueueStatus["QueuedForReview"] = "queued_for_review";
})(DataRequestQueueStatus || (exports.DataRequestQueueStatus = DataRequestQueueStatus = {}));
let DataRequest = class DataRequest {
};
exports.DataRequest = DataRequest;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], DataRequest.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)('int'),
    __metadata("design:type", Number)
], DataRequest.prototype, "dataRequestTypeId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar' }),
    __metadata("design:type", String)
], DataRequest.prototype, "dataRequestType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: DataRequestStatus }),
    __metadata("design:type", String)
], DataRequest.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', default: '' }),
    __metadata("design:type", String)
], DataRequest.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], DataRequest.prototype, "customUserRemark", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], DataRequest.prototype, "dueDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], DataRequest.prototype, "responsiblePersonId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], DataRequest.prototype, "approvedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], DataRequest.prototype, "approvedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: DataRequestQueueStatus,
        default: null,
    }),
    __metadata("design:type", String)
], DataRequest.prototype, "queueStatus", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], DataRequest.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], DataRequest.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid'),
    __metadata("design:type", String)
], DataRequest.prototype, "projectId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], DataRequest.prototype, "content_version", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => datarequest_generation_entity_1.DataRequestGeneration, (dataRequestGeneration) => dataRequestGeneration.dataRequest),
    __metadata("design:type", Array)
], DataRequest.prototype, "dataRequestGenerations", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => esrs_disclosure_requirement_entity_1.ESRSDisclosureRequirement),
    (0, typeorm_1.JoinColumn)({ name: 'dataRequestTypeId' }),
    __metadata("design:type", esrs_disclosure_requirement_entity_1.ESRSDisclosureRequirement)
], DataRequest.prototype, "disclosureRequirement", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.dataRequests),
    (0, typeorm_1.JoinColumn)({ name: 'responsiblePersonId' }),
    __metadata("design:type", user_entity_1.User)
], DataRequest.prototype, "responsiblePerson", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.dataRequests),
    (0, typeorm_1.JoinColumn)({ name: 'approvedBy' }),
    __metadata("design:type", user_entity_1.User)
], DataRequest.prototype, "approver", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => project_entity_1.Project, (project) => project.dataRequests),
    (0, typeorm_1.JoinColumn)({ name: 'projectId' }),
    __metadata("design:type", project_entity_1.Project)
], DataRequest.prototype, "project", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => datapoint_request_entity_1.DatapointRequest, (datapointRequest) => datapointRequest.dataRequest),
    __metadata("design:type", Array)
], DataRequest.prototype, "datapointRequests", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => comment_entity_1.Comment, (comment) => comment.dataRequest, {
        nullable: true,
        createForeignKeyConstraints: false,
    }),
    __metadata("design:type", Array)
], DataRequest.prototype, "comments", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => comment_generation_entity_1.CommentGeneration, (commentGeneration) => commentGeneration.dataRequest, {
        nullable: true,
        createForeignKeyConstraints: false,
    }),
    __metadata("design:type", Array)
], DataRequest.prototype, "commentGenerations", void 0);
exports.DataRequest = DataRequest = __decorate([
    (0, typeorm_1.Entity)()
], DataRequest);
//# sourceMappingURL=data-request.entity.js.map