import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Not, Repository } from 'typeorm';
import {
  DatapointQueueStatus,
  DatapointRequest,
  DatapointRequestStatus,
} from 'src/datapoint/entities/datapoint-request.entity';
import { ChatCompletionMessageParam } from 'openai/resources';
import { PromptService } from 'src/prompts/prompts.service';
import { ProjectService } from 'src/project/project.service';
import { Comment, CommentType } from 'src/project/entities/comment.entity';
import {
  extractCitationsFromReportTextGeneration,
  trimHtmlPreAndPostfix,
} from 'src/util/llm-response-util';
import { UsersService } from 'src/users/users.service';
import { User } from 'src/users/entities/user.entity';
import { WorkspaceService } from 'src/workspace/workspace.service';
import { MDRPromptService } from 'src/prompts/mdr-prompts.service';
import { NumericsPromptService } from 'src/prompts/numerics-prompts.service';
import { NormalDpPromptService } from 'src/prompts/datapoint-generation-prompts.service';
import { TablePromptService } from 'src/prompts/table-prompts.service';
import { ESRSDatapoint } from 'src/datapoint/entities/esrs-datapoint.entity';
import { ESRSTopic } from 'src/esrs/entities/esrs-topic.entity';
import { USER_ROLES } from 'src/constants';
import { DatapointGeneration } from 'src/datapoint/entities/datapoint-generation.entity';
import { LLM_MODELS } from 'src/constants';
import { LlmRateLimiterService } from 'src/llm/services/llm-rate-limiter.service';
import { PromptManagementService } from 'src/prompt-management/prompt-management.service';
import { TimingTracker } from 'src/util/timing-util';
import { Company } from 'src/workspace/entities/company.entity';
import { DatapointTypeHelper } from './utils/datapoint-type.helper';
import { DatapointStatusService } from './datapoint-status.service';
import { DatapointDocumentService } from './datapoint-document.service';
import { MaterialTopicsService } from './material-topics.service';
import { PromptProcessingUtilsService } from './services/prompt-processing-utils.service';
import { WorkerLogger } from 'src/shared/logger.service';

@Injectable()
export class DatapointGenerationService {
  constructor(
    @InjectRepository(DatapointRequest)
    private readonly datapointRequestRepository: Repository<DatapointRequest>,
    @InjectRepository(ESRSDatapoint)
    private readonly esrsDatapointRepository: Repository<ESRSDatapoint>,
    @InjectRepository(DatapointGeneration)
    private readonly datapointGenerationRepository: Repository<DatapointGeneration>,
    private readonly promptService: PromptService,
    private readonly mdrPromptService: MDRPromptService,
    private readonly NormalDpPromptService: NormalDpPromptService,
    private readonly tablePromptService: TablePromptService,
    private readonly datapointPromptService: NumericsPromptService,
    private readonly projectService: ProjectService,
    private readonly userService: UsersService,
    private readonly workspaceService: WorkspaceService,
    private readonly llmRateLimiterService: LlmRateLimiterService,
    private readonly promptManagementService: PromptManagementService,
    private readonly datapointTypeHelper: DatapointTypeHelper,
    private readonly datapointStatusService: DatapointStatusService,
    private readonly datapointDocumentService: DatapointDocumentService,
    private readonly materialTopicsService: MaterialTopicsService,
    private readonly promptProcessingUtilsService: PromptProcessingUtilsService
  ) {}

  private readonly logger = new WorkerLogger(DatapointGenerationService.name);

  async reviewDatapointContentWithAI({
    datapointRequestId,
    userId,
    workspaceId,
  }: {
    datapointRequestId: string;
    userId: string;
    workspaceId: string;
  }): Promise<Comment[]> {
    const isAIEvaluator = await this.userService.userHasRequiredRole(userId, [
      USER_ROLES.SuperAdmin,
    ]);

    const datapointRequest = await this.datapointRequestRepository.findOne({
      where: {
        id: datapointRequestId,
        status: Not(DatapointRequestStatus.NotResponded),
      },
      relations: {
        dataRequest: { project: true },
        esrsDatapoint: {
          topicRelations: {
            topic: true,
          },
        },
        comments: true,
      },
    });

    const companyDetails =
      await this.workspaceService.getCompanyDetailFromWorkspaceId(workspaceId);
    const { generalCompanyProfile } = companyDetails;

    // Build prompt
    this.logger.log(
      `Start Gap Analysis for datapointRequest ${datapointRequest.id}`
    );

    const otherDatapoints = await this.esrsDatapointRepository.find({
      where: {
        esrsDisclosureRequirementId:
          datapointRequest.esrsDatapoint.esrsDisclosureRequirementId,
        id: Not(datapointRequest.esrsDatapoint.id),
      },
    });

    let datapointRequestGapAnalysisChatCompletion: ChatCompletionMessageParam[] =
      [];
    const esrsDatapoint = datapointRequest.esrsDatapoint;
    const h2Count = (datapointRequest.content.match(/<h2>/g) || []).length;
    if (esrsDatapoint.datapointId.includes('MDR') && h2Count > 0) {
      this.logger.log(
        `MDR Datapoint identified: ${esrsDatapoint.datapointId} with ${h2Count} policies`
      );
      datapointRequestGapAnalysisChatCompletion = [
        {
          role: 'system',
          content:
            this.mdrPromptService.indentifyMDRandGenerateGapAnalysisSystemPrompt(
              {
                esrsDatapoint,
                language:
                  datapointRequest.dataRequest.project.primaryContentLanguage,
              }
            ),
        },
        {
          role: 'user',
          content:
            this.promptService.generateDatapointGapAnalysisContentContext(
              datapointRequest.content
            ),
        },
      ];
    } else {
      datapointRequestGapAnalysisChatCompletion = [
        {
          role: 'system',
          content: this.promptService.generateDatapointGapAnalysisSystemPrompt1(
            {
              esrsDatapoint,
              generationLanguage:
                datapointRequest.dataRequest.project.primaryContentLanguage,
            }
          ),
        },
        // TODO: Attach all the gaps that were already analysed and are still not resolved, so they are not added multiple times
        {
          role: 'system',
          content:
            this.promptService.generateDatapointGapAnalysisDatapointSpecificSystemPrompt(
              esrsDatapoint,
              otherDatapoints
            ),
        },
        {
          role: 'user',
          content:
            this.promptService.generateDatapointGapAnalysisContentContext(
              datapointRequest.content
            ),
        },
      ];
    }

    if (esrsDatapoint.conditional) {
      datapointRequestGapAnalysisChatCompletion.push({
        role: 'system',
        content: `This is a conditional datapoint. If there is information missing, it is up to the user whether they are ok with a gap, or not. The conditions typically have "if relevant" or similar conditionals in the law-text or application-requirements. Explicitly mention in this gap analysis whether the condition is met or not met.`,
      });
    }

    const topicRelations = datapointRequest.esrsDatapoint.topicRelations;
    const materialTopicsInHierarchy =
      await this.materialTopicsService.generateHierarchicalListOfTopics({
        topicRelations,
        projectId: datapointRequest.dataRequest.project.id,
        material: true,
      });

    if (materialTopicsInHierarchy.length > 0) {
      datapointRequestGapAnalysisChatCompletion.push({
        role: 'system',
        content:
          this.promptService.datapointRequestGapAnalysisSystemPromptMaterialTopics(
            { topics: materialTopicsInHierarchy, material: true }
          ),
      });

      const nonMaterialTopicsInHierarchy =
        await this.materialTopicsService.generateHierarchicalListOfTopics({
          topicRelations,
          projectId: datapointRequest.dataRequest.project.id,
          material: false,
        });

      if (nonMaterialTopicsInHierarchy.length > 0) {
        datapointRequestGapAnalysisChatCompletion.push({
          role: 'system',
          content:
            this.promptService.datapointRequestGapAnalysisSystemPromptMaterialTopics(
              { topics: nonMaterialTopicsInHierarchy, material: false }
            ),
        });
      }
    }

    datapointRequestGapAnalysisChatCompletion.push({
      role: 'system',
      content: this.promptService.generateDatapointGapAnalysisSystemPrompt2({
        esrsDatapoint,
        generationLanguage:
          datapointRequest.dataRequest.project.primaryContentLanguage,
        reportTextGenerationRules:
          datapointRequest.dataRequest.project.reportTextGenerationRules,
        generalCompanyProfile,
        reportingYear: datapointRequest.dataRequest.project.reportingYear,
      }),
    });

    const gapAnalysisCompletionResponse =
      await this.llmRateLimiterService.handleRequest({
        model: LLM_MODELS['o3-mini'],
        messages: datapointRequestGapAnalysisChatCompletion,
        json: true,
        temperature: 0.3,
      });

    if (gapAnalysisCompletionResponse.status === 400) {
      throw new Error(gapAnalysisCompletionResponse.response);
    }

    // Save comment
    this.logger.log(
      `Gap Analysis Tokens: ${JSON.stringify(gapAnalysisCompletionResponse.token)}`
    );

    type GapInformation = {
      gap?: string;
      actions?: string[];
      exampleText?: string;
      text?: string;
      disclaimer?: string;
      title?: string;
    };

    const gapAnalysis: GapInformation & {
      gapIdentified: boolean;
      gaps?: GapInformation[];
    } = gapAnalysisCompletionResponse.response;

    //replace ```html if the response escaped the html
    // if (gapAnalysis) {
    //   gapAnalysis = gapAnalysis.replace(`\```html`, '');
    //   gapAnalysis = gapAnalysis.replace(`\```, '');
    // }

    const globalAIUser: User = await this.userService.findGlobalGlacierAIUser();

    const comments: Comment[] = [];
    let gapHtml = '';

    if (
      gapAnalysis.gapIdentified &&
      gapAnalysis.gaps &&
      gapAnalysis.gaps.length > 0
    ) {
      for (const gap of gapAnalysis.gaps) {
        // prettier-ignore
        gapHtml =
          (gap.title ? "<h3> " + gap.title + "</h3>" : "") +
          "<p><strong>Gap Identified:</strong> " + gap.gap + "</p>" +
          "<p><strong>Recommended Actions:</strong></p>" +
          (gap.actions ?
            "<ul>" +
            gap.actions.map(function (action) {
              return "<li>" + action + "</li>";
            }).join('') +
            "</ul>" :
            "") +
          "<p><strong>Example Text:</strong>" + gap.exampleText + "</p>" +
          (gap.disclaimer ? "<p><strong>Disclaimer:</strong> " + gap.disclaimer + "</p>" : "");

        const comment = await this.projectService.addComment({
          commentableId: datapointRequestId,
          commentableType: CommentType.DatapointRequest,
          userId: globalAIUser.id,
          comment: gapHtml,
          workspaceId,
          evaluationLot: isAIEvaluator,
        });

        comments.push(comment);
      }
      // TODO: need to validate and combine both if conditions to one
    } else if (gapAnalysis.gapIdentified) {
      // prettier-ignore
      gapHtml =
        (gapAnalysis.title ? "<h3> " + gapAnalysis.title + "</h3>" : "") +
        "<p><strong>Gap Identified:</strong> " + gapAnalysis.gap + "</p>" +
        "<p><strong>Recommended Actions:</strong></p>" +
        (gapAnalysis.actions ?
          "<ul>" +
          gapAnalysis.actions.map(function (action) {
            return "<li>" + action + "</li>";
          }).join('') +
          "</ul>" :
          "") +
        "<p><strong>Example Text:</strong>" + gapAnalysis.exampleText + "</p>" +
        (gapAnalysis.disclaimer ? "<p><strong>Disclaimer:</strong> " + gapAnalysis.disclaimer + "</p>" : "");

      const comment = await this.projectService.addComment({
        commentableId: datapointRequestId,
        commentableType: CommentType.DatapointRequest,
        userId: globalAIUser.id,
        comment: gapHtml,
        workspaceId,
        evaluationLot: isAIEvaluator,
      });

      comments.push(comment);
    } else {
      // prettier-ignore
      gapHtml =
        "<p><strong>No Gap Identified:</strong></p>" +
        "<p>" + gapAnalysis.text + "</p>";

      const comment = await this.projectService.addComment({
        commentableId: datapointRequestId,
        commentableType: CommentType.DatapointRequest,
        userId: globalAIUser.id,
        comment: gapHtml,
        workspaceId,
        evaluationLot: isAIEvaluator,
      });

      comments.push(comment);
    }
    if (datapointRequest.queueStatus === DatapointQueueStatus.QueuedForReview) {
      await this.datapointStatusService.updateQueueStatus({
        datapointRequestId,
        queueStatus: null,
      });
    }
    return comments;
  }

  async generateDatapointContentWithControlledAI({
    datapointRequestId,
    userId,
    workspaceId,
    useExistingReportTextForReference,
    dryRun = false,
    testPrompts,
  }: {
    datapointRequestId: string;
    userId: string;
    workspaceId: string;
    useExistingReportTextForReference: boolean;
    dryRun?: boolean;
    testPrompts?: Record<
      string,
      { prompt: string; chainIdentifier: string; model: LLM_MODELS }
    >;
  }): Promise<void | {
    content: string;
    metadata?: any;
    prompts: ChatCompletionMessageParam[];
    timings?: any;
  }> {
    // Initialize timing tracker for dry runs
    const timingTracker = dryRun ? new TimingTracker() : null;

    // Initialize array to track used prompt IDs
    const usedPromptIds: string[] = [];

    if (timingTracker) {
      timingTracker.start('Load Datapoint Request');
    }

    const datapointRequest = await this.datapointRequestRepository.findOne({
      where: { id: datapointRequestId },
      relations: {
        dataRequest: { project: true },
        esrsDatapoint: {
          esrsDisclosureRequirement: true,
          topicRelations: {
            topic: true,
          },
        },
        datapointDocumentChunkMap: {
          documentChunk: { document: true },
        },
      },
    });

    if (timingTracker) {
      timingTracker.end('Load Datapoint Request');
      timingTracker.start('Load Company Details');
    }

    const companyDetails =
      await this.workspaceService.getCompanyDetailFromWorkspaceId(workspaceId);
    const { generalCompanyProfile } = companyDetails;

    if (timingTracker) {
      timingTracker.end('Load Company Details');
      timingTracker.start('Load Related Datapoints');
    }

    const relatedDatapoints = await this.datapointRequestRepository.find({
      where: {
        dataRequestId: datapointRequest.dataRequestId,
        id: Not(datapointRequest.id),
        content: Not(''),
      },
      relations: ['esrsDatapoint'],
    });

    if (timingTracker) {
      timingTracker.end('Load Related Datapoints');
    }

    this.logger.log(
      `Start Generating datapointRequest ${datapointRequest.id} with AI`
    );

    if (timingTracker) {
      timingTracker.start('Load Other Datapoints');
    }

    const otherDatapoints = await this.esrsDatapointRepository.find({
      where: {
        esrsDisclosureRequirementId:
          datapointRequest.esrsDatapoint.esrsDisclosureRequirementId,
        id: Not(datapointRequest.esrsDatapoint.id),
      },
    });

    if (timingTracker) {
      timingTracker.end('Load Other Datapoints');
      timingTracker.start('Generate Document Context');
    }

    //Generate Context from DocumentLinks
    const { context: linkedChunksContext, documentChunksIndex } =
      await this.datapointDocumentService.generateDatapointGenerationContextFromLinkedDocumentChunks(
        datapointRequest,
        timingTracker
      );

    if (timingTracker) {
      timingTracker.end('Generate Document Context');
      timingTracker.start('Generate Material Topics Hierarchy');
    }

    //Generate Prompt based on Requirements
    const topicRelations = datapointRequest.esrsDatapoint.topicRelations;
    const materialTopicsInHierarchy =
      await this.materialTopicsService.generateHierarchicalListOfTopics({
        topicRelations,
        projectId: datapointRequest.dataRequest.project.id,
        material: true,
      });

    const nonMaterialTopicsInHierarchy =
      await this.materialTopicsService.generateHierarchicalListOfTopics({
        topicRelations,
        projectId: datapointRequest.dataRequest.project.id,
        material: false,
      });

    if (timingTracker) {
      timingTracker.end('Generate Material Topics Hierarchy');
    }

    const esrsDatapoint = datapointRequest.esrsDatapoint;

    if (timingTracker) {
      timingTracker.start('Build Prompts');
    }

    const { prompts, promptIds } = await this.buildDatapointGenerationPrompts({
      datapointRequest,
      esrsDatapoint,
      materialTopicsInHierarchy,
      nonMaterialTopicsInHierarchy,
      useExistingReportTextForReference,
      relatedDatapoints,
      linkedChunksContext,
      otherDatapoints,
      generalCompanyProfile,
      companyDetails,
      testPrompts,
      dryRun,
      timingTracker,
    });

    // Add prompt IDs from buildDatapointGenerationPrompts
    usedPromptIds.push(...promptIds);

    if (timingTracker) {
      timingTracker.end('Build Prompts');
    }

    // Determine which model to use
    const feature = this.promptProcessingUtilsService.determineFeatureType(
      esrsDatapoint,
      datapointRequest.content || '',
      'generation'
    );

    // Determine which model to use for A1
    const finalModel = await this.promptProcessingUtilsService.getModelForChain(
      this.promptManagementService,
      feature,
      'A1',
      testPrompts,
      dryRun
    );

    if (timingTracker) {
      timingTracker.start('LLM Request - Initial Generation');
    }

    let predatapointGenerationChatCompletionResponse;
    try {
      predatapointGenerationChatCompletionResponse =
        await this.llmRateLimiterService.handleRequest({
          model: finalModel,
          messages: prompts,
          json: true, //json true and do corresponding handling for 1) desc 2) gap analysis 3) desc 2
          temperature: 0,
        });

      if (timingTracker) {
        timingTracker.end('LLM Request - Initial Generation');
      }

      if (predatapointGenerationChatCompletionResponse.status === 400) {
        throw new Error(predatapointGenerationChatCompletionResponse.response);
      }
    } catch (error) {
      // Handle error during dry run
      if (dryRun) {
        return {
          content: `Error during initial generation: ${error.message || 'Unknown error'} `,
          metadata: {
            error: error.message || 'Unknown error',
            stage: 'initial_generation',
            promptIds: usedPromptIds,
          },
          prompts: prompts,
          timings:
            this.promptProcessingUtilsService.extractTimings(timingTracker),
        };
      }
      // If not dry run, rethrow the error
      throw error;
    }

    let datapointGenerationChatCompletionResponse;

    // // if not esrsDatapoint.datapointId.includes('MDR')
    // if (!esrsDatapoint.datapointId.includes('MDR')) {
    //   datapointGenerationChatCompletionResponse =
    //     predatapointGenerationChatCompletionResponse;
    // } else {
    // Build B series prompts for MDR improving formatting
    let improvingFormattingPrompt: ChatCompletionMessageParam[] = [];
    let b1Prompt: string;
    let b1PromptId: string | null = null;

    if (dryRun && testPrompts && testPrompts['B1']) {
      // In dry run mode, compile the B1 prompt directly
      b1Prompt = await this.promptManagementService.compilePromptContent({
        promptContent: testPrompts['B1'].prompt,
        variables: {
          esrsDatapoint: {
            ...esrsDatapoint,
            companyName: companyDetails?.name || 'The company',
          },
          generationLanguage:
            datapointRequest.dataRequest.project.primaryContentLanguage,
          reportTextGenerationRules:
            datapointRequest.dataRequest.project.reportTextGenerationRules,
          customUserRemark: datapointRequest.customUserRemark || '',
          generalCompanyProfile: generalCompanyProfile,
          currentContent: datapointRequest.content,
          reportingYear: datapointRequest.dataRequest.project.reportingYear,
          predatapointGenerationChatCompletionResponse:
            predatapointGenerationChatCompletionResponse.response['datapoint'],
        },
      });
    } else {
      // In normal mode, fetch from database
      const basePrompt =
        await this.promptManagementService.findByFeatureAndChain(feature, 'B1');
      b1PromptId = basePrompt.id;
      b1Prompt = await this.promptManagementService.compilePromptContent({
        promptContent: basePrompt.prompt,
        variables: {
          esrsDatapoint: {
            ...esrsDatapoint,
            companyName: companyDetails?.name || 'The company',
          },
          generationLanguage:
            datapointRequest.dataRequest.project.primaryContentLanguage,
          reportTextGenerationRules:
            datapointRequest.dataRequest.project.reportTextGenerationRules,
          customUserRemark: datapointRequest.customUserRemark || '',
          generalCompanyProfile: generalCompanyProfile,
          currentContent: datapointRequest.content,
          reportingYear: datapointRequest.dataRequest.project.reportingYear,
          predatapointGenerationChatCompletionResponse:
            predatapointGenerationChatCompletionResponse.response['datapoint'],
        },
      });
    }

    // Track B1 prompt ID if not in dry run mode
    if (!dryRun && b1PromptId) {
      usedPromptIds.push(b1PromptId);
    }

    improvingFormattingPrompt = [
      {
        role: 'system',
        content: b1Prompt,
      },
    ];

    if (timingTracker) {
      timingTracker.start('LLM Request - Formatting Improvement');
    }

    // Determine model for B1 call
    const b1Model = await this.promptProcessingUtilsService.getModelForChain(
      this.promptManagementService,
      feature,
      'B1',
      testPrompts,
      dryRun,
      finalModel
    );

    try {
      datapointGenerationChatCompletionResponse =
        await this.llmRateLimiterService.handleRequest({
          model: b1Model,
          messages: improvingFormattingPrompt,
          json: true,
          temperature: 0,
        });

      if (timingTracker) {
        timingTracker.end('LLM Request - Formatting Improvement');
      }

      if (datapointGenerationChatCompletionResponse.status === 400) {
        throw new Error(datapointGenerationChatCompletionResponse.response);
      }
    } catch (error) {
      // Handle error during dry run
      if (dryRun) {
        return {
          content: `Error during formatting improvement: ${error.message || 'Unknown error'} `,
          metadata: {
            error: error.message || 'Unknown error',
            stage: 'formatting_improvement',
            promptIds: usedPromptIds,
            initialGeneration:
              predatapointGenerationChatCompletionResponse.response,
          },
          prompts: [...prompts, ...improvingFormattingPrompt],
          timings:
            this.promptProcessingUtilsService.extractTimings(timingTracker),
        };
      }
      // If not dry run, rethrow the error
      throw error;
    }

    if (timingTracker) {
      timingTracker.start('Process Response');
    }

    if (
      datapointGenerationChatCompletionResponse.response[
        'datapoint'
      ].startsWith('<br>')
    ) {
      datapointGenerationChatCompletionResponse.response['datapoint'] =
        datapointGenerationChatCompletionResponse.response['datapoint'].slice(
          4
        );
    }

    let { reportText: generatedContent, citation: generatedCitation } =
      extractCitationsFromReportTextGeneration(
        datapointGenerationChatCompletionResponse.response['datapoint'],
        documentChunksIndex
      );

    if (datapointGenerationChatCompletionResponse.response['citation']) {
      const citation =
        datapointGenerationChatCompletionResponse.response['citation'];
      generatedCitation = { ...generatedCitation, ...citation };
    }

    generatedContent = trimHtmlPreAndPostfix(generatedContent);

    if (timingTracker) {
      timingTracker.end('Process Response');
    }

    datapointRequest.content = generatedContent;
    let datapointMetadata;
    if (datapointRequest.metadata) {
      datapointMetadata = JSON.parse(datapointRequest.metadata);
      datapointMetadata.citation = generatedCitation;
      datapointMetadata.promptIds = usedPromptIds;
      datapointMetadata = JSON.stringify(datapointMetadata);
    } else {
      datapointMetadata = JSON.stringify({
        citation: generatedCitation,
        promptIds: usedPromptIds,
      });
    }
    // If this is a dry run, return the generated content without saving
    if (dryRun) {
      const response: any = {
        content: generatedContent,
        metadata: JSON.parse(datapointMetadata),
        prompts: [...prompts, ...improvingFormattingPrompt],
        timings:
          this.promptProcessingUtilsService.extractTimings(timingTracker),
      };

      return response;
    }

    const isSuperAdmin = await this.userService.userHasRequiredRole(userId, [
      USER_ROLES.SuperAdmin,
    ]);

    //Since we use the same function for both superadmin and non-superadmin users, we need to create these optional proerties
    const generatedDataResponse: {
      id: string;
      content: string;
      metadata: string;
    } = {
      content: generatedContent,
      metadata: datapointMetadata,
      id: datapointRequestId,
    };
    if (isSuperAdmin) {
      const dataGeneration = await this.datapointGenerationRepository.create({
        data: { content: generatedContent, metadata: datapointMetadata },
        datapointRequest: datapointRequest,
        evaluatorId: userId,
      });
      await this.datapointGenerationRepository.save(dataGeneration);
      generatedDataResponse.id = dataGeneration.id;
    } else {
      datapointRequest.metadata = datapointMetadata;
      datapointRequest.content = generatedContent;
      await this.datapointRequestRepository.save(datapointRequest);
    }

    //TODO: This should be passed as a success callback to the queue service
    await this.datapointStatusService.updateQueueStatus({
      datapointRequestId,
      queueStatus: null,
    });

    // comment this out if you don't find the preGapAnalysis useful
    //if (datapointGenerationChatCompletionResponse.response['preGapAnalysis']) {
    //  const globalAIUser:
    //    await this.userService.findGlobalGlacierAIUser();

    //  await this.projectService.addComment({
    //    commentableId: datapointRequestId,
    //    commentableType: CommentType.DatapointRequest,
    //    userId: globalAIUser.id,
    //    comment:
    //      datapointGenerationChatCompletionResponse.response['preGapAnalysis'],
    //    workspaceId,
    //  });
    //}

    this.logger.log(
      `Finished Generating datapointRequest ${datapointRequest.id} with AI`
    );

    const globalAIUser: User = await this.userService.findGlobalGlacierAIUser();

    await this.workspaceService.storeActionHistory({
      event: 'datapoint_request_ai_generated',
      ref: datapointRequestId,
      workspaceId: workspaceId,
      versionData: {
        event: isSuperAdmin
          ? 'datapoint_request_ai_generated'
          : 'datapoint_request_updated',
        doneBy: globalAIUser.id,
        issuedBy: userId,
        data: datapointRequest,
      },
    });
  }

  async generateDatapointContentWithAI({
    datapointRequestId,
    userId,
    workspaceId,
    useExistingReportTextForReference,
  }: {
    datapointRequestId: string;
    userId: string;
    workspaceId: string;
    useExistingReportTextForReference: boolean;
  }): Promise<void> {
    const datapointRequest = await this.datapointRequestRepository.findOne({
      where: { id: datapointRequestId },
      relations: {
        dataRequest: { project: true },
        esrsDatapoint: {
          esrsDisclosureRequirement: true,
          topicRelations: {
            topic: true,
          },
        },
        datapointDocumentChunkMap: {
          documentChunk: { document: true },
        },
      },
    });
    const { generalCompanyProfile } =
      await this.workspaceService.getCompanyDetailFromWorkspaceId(workspaceId);
    const relatedDatapoints = await this.datapointRequestRepository.find({
      where: {
        dataRequestId: datapointRequest.dataRequestId,
        id: Not(datapointRequest.id),
        content: Not(''),
      },
      relations: ['esrsDatapoint'],
    });

    this.logger.log(
      `Start Generating datapointRequest ${datapointRequest.id} with AI`
    );

    const otherDatapoints = await this.esrsDatapointRepository.find({
      where: {
        esrsDisclosureRequirementId:
          datapointRequest.esrsDatapoint.esrsDisclosureRequirementId,
        id: Not(datapointRequest.esrsDatapoint.id),
      },
    });

    //Generate Context from DocumentLinks
    const {
      context: datapointGenerationContextFromLinkedDocumentChunks,
      documentChunksIndex,
    } =
      await this.datapointDocumentService.generateDatapointGenerationContextFromLinkedDocumentChunks(
        datapointRequest
      );
    //Generate Prompt based on Requirements
    let datapointGenerationChatCompletion: ChatCompletionMessageParam[] = [];

    const topicRelations = datapointRequest.esrsDatapoint.topicRelations;
    const materialTopicsInHierarchy =
      await this.materialTopicsService.generateHierarchicalListOfTopics({
        topicRelations,
        projectId: datapointRequest.dataRequest.projectId,
        material: true,
      });

    const nonMaterialTopicsInHierarchy =
      await this.materialTopicsService.generateHierarchicalListOfTopics({
        topicRelations,
        projectId: datapointRequest.dataRequest.projectId,
        material: false,
      });

    const esrsDatapoint = datapointRequest.esrsDatapoint;
    const h2Count = (datapointRequest.content.match(/<h2>/g) || []).length;
    const isNumeric = this.datapointTypeHelper.isNumericDataPoint(
      esrsDatapoint.dataType
    );
    const isTabular = esrsDatapoint.dataType?.includes('table');

    if (esrsDatapoint.datapointId.includes('MDR') && h2Count > 0) {
      this.logger.log(
        `MDR Datapoint identified: ${esrsDatapoint.datapointId} with ${h2Count} policies`
      );

      datapointGenerationChatCompletion = [
        {
          role: 'system',
          content:
            this.mdrPromptService.indentifyMDRContentGenerationMainPrompt({
              // instead of separate roles, all in one prompt. Shouldn't make a big difference
              esrsDatapoint,
              datapointGenerationContextFromLinkedDocumentChunks,
              language:
                datapointRequest.dataRequest.project.primaryContentLanguage,
              reportTextGenerationRules:
                datapointRequest.dataRequest.project.reportTextGenerationRules,
              generalCompanyProfile,
              reportingYear: datapointRequest.dataRequest.project.reportingYear,
              customUserRemark: datapointRequest.customUserRemark,
            }),
        },
        //{
        //  role: 'system',
        //  content: this.mdrPromptService.generateCitationPrompt(),
        //},
      ];
    } else if (isTabular) {
      this.logger.log(
        `Datapoint identified: ${esrsDatapoint.datapointId}${isTabular ? ' (Tabular)' : isNumeric ? ' (Numeric)' : 'Other'}`
      );

      datapointGenerationChatCompletion = [
        {
          role: 'system',
          content: this.tablePromptService.generateTableSystemPrompt({
            esrsDatapoints: [esrsDatapoint],
            generationLanguage:
              datapointRequest.dataRequest.project.primaryContentLanguage,
            reportTextGenerationRules:
              datapointRequest.dataRequest.project.reportTextGenerationRules,
            customUserRemark: datapointRequest.customUserRemark,
            currentContent: datapointRequest.content,
            linkedChunks: datapointGenerationContextFromLinkedDocumentChunks,
            otherDatapoints,
            reportingYear: datapointRequest.dataRequest.project.reportingYear,
            generalCompanyProfile,
          }),
        },
      ];
    } else if (isNumeric) {
      this.logger.log(
        `Datapoint identified: ${esrsDatapoint.datapointId}${isTabular ? ' (Tabular)' : isNumeric ? ' (Numeric)' : 'Other'}`
      );
      datapointGenerationChatCompletion = [
        {
          role: 'system',
          content:
            this.datapointPromptService.generateNumericDatapointContentGenerationSystemPrompt(
              {
                esrsDatapoint: esrsDatapoint,
                generationLanguage:
                  datapointRequest.dataRequest.project.primaryContentLanguage,
                reportTextGenerationRules:
                  datapointRequest.dataRequest.project
                    .reportTextGenerationRules,
                customUserRemark: datapointRequest.customUserRemark,
                currentContent: datapointRequest.content,
                generalCompanyProfile,
                otherDatapoints: otherDatapoints,
                reportingYear:
                  datapointRequest.dataRequest.project.reportingYear,
                linkedChunks:
                  datapointGenerationContextFromLinkedDocumentChunks,
              }
            ),
        },
      ];
    } else {
      // no MDR, not numeric
      datapointGenerationChatCompletion = [
        {
          role: 'system',
          content:
            this.NormalDpPromptService.generateDatapointContentGenerationSystemPrompt(
              {
                esrsDatapoint: esrsDatapoint,
                generationLanguage:
                  datapointRequest.dataRequest.project.primaryContentLanguage,
                reportTextGenerationRules:
                  datapointRequest.dataRequest.project
                    .reportTextGenerationRules,
                customUserRemark: datapointRequest.customUserRemark,
                currentContent: datapointRequest.content,
                otherDatapoints: otherDatapoints,
                reportingYear:
                  datapointRequest.dataRequest.project.reportingYear,
                generalCompanyProfile,
                linkedChunks:
                  datapointGenerationContextFromLinkedDocumentChunks,
              }
            ),
        },
      ];
    }

    if (materialTopicsInHierarchy.length > 0) {
      datapointGenerationChatCompletion.push({
        role: 'system',
        content:
          this.promptService.datapointRequestContentGenerationSystemPromptMaterialTopics(
            { topics: materialTopicsInHierarchy, material: true }
          ),
      });

      if (nonMaterialTopicsInHierarchy.length > 0) {
        datapointGenerationChatCompletion.push({
          role: 'system',
          content:
            this.promptService.datapointRequestContentGenerationSystemPromptMaterialTopics(
              { topics: nonMaterialTopicsInHierarchy, material: false }
            ),
        });
      }
    }

    if (useExistingReportTextForReference) {
      datapointGenerationChatCompletion.push({
        role: 'system',
        content: `Following is the existing report text that was previously generated, user wishes to extend upon this:\n ${datapointRequest.content}`,
      });
    }

    if (esrsDatapoint.conditional) {
      datapointGenerationChatCompletion.push({
        role: 'system',
        content: `This is a conditional datapoint. This means that information described in the law text might not be neccessary to disclose, if the conditions aren't met. The conditions typically have "if relevant" or similar conditionals in the phrasing of law-text or application-requirements. It is up to the user to decide whether they are ok with a gap, or not.  Thus for apparent gaps here, explicitly mention in this‚ gap analysis whether the condition is met or not met.`,
      });
    }

    if (relatedDatapoints.length > 0) {
      datapointGenerationChatCompletion.push({
        role: 'system',
        content:
          this.promptService.datapointRequestContentGenerationSystemPromptRelatedDatapoints(
            relatedDatapoints
          ),
      });
    }

    const predatapointGenerationChatCompletionResponse =
      await this.llmRateLimiterService.handleRequest({
        model: LLM_MODELS.o3,
        messages: datapointGenerationChatCompletion,
        json: true, //json true and do corresponding handling for 1) desc 2) gap analysis 3) desc 2
        temperature: 0,
      });
    if (predatapointGenerationChatCompletionResponse.status === 400) {
      throw new Error(predatapointGenerationChatCompletionResponse.response);
    }

    let datapointGenerationChatCompletionResponse;

    // if not esrsDatapoint.datapointId.includes('MDR')
    if (!esrsDatapoint.datapointId.includes('MDR')) {
      datapointGenerationChatCompletionResponse =
        predatapointGenerationChatCompletionResponse;
    } else {
      let improvingFormattingPrompt: ChatCompletionMessageParam[] = [];
      improvingFormattingPrompt = [
        {
          role: 'system',
          content: this.NormalDpPromptService.improvingFormattingPrompt({
            esrsDatapoint: esrsDatapoint,
            generationLanguage:
              datapointRequest.dataRequest.project.primaryContentLanguage,
            reportTextGenerationRules:
              datapointRequest.dataRequest.project.reportTextGenerationRules,
            customUserRemark: datapointRequest.customUserRemark,
            generalCompanyProfile,
            currentContent: datapointRequest.content,
            reportingYear: datapointRequest.dataRequest.project.reportingYear,
            predatapointGenerationChatCompletionResponse:
              predatapointGenerationChatCompletionResponse.response[
                'datapoint'
              ],
          }),
        },
      ];

      datapointGenerationChatCompletionResponse =
        await this.llmRateLimiterService.handleRequest({
          model: LLM_MODELS['o3'],
          messages: improvingFormattingPrompt,
          json: true,
          temperature: 0,
        });
    }

    if (datapointGenerationChatCompletionResponse.status === 400) {
      throw new Error(datapointGenerationChatCompletionResponse.response);
    }

    if (
      datapointGenerationChatCompletionResponse.response[
        'datapoint'
      ].startsWith('<br>')
    ) {
      datapointGenerationChatCompletionResponse.response['datapoint'] =
        datapointGenerationChatCompletionResponse.response['datapoint'].slice(
          4
        );
    }

    let { reportText: generatedContent, citation: generatedCitation } =
      extractCitationsFromReportTextGeneration(
        datapointGenerationChatCompletionResponse.response['datapoint'],
        documentChunksIndex
      );

    if (datapointGenerationChatCompletionResponse.response['citation']) {
      const citation =
        datapointGenerationChatCompletionResponse.response['citation'];
      generatedCitation = { ...generatedCitation, ...citation };
    }

    generatedContent = trimHtmlPreAndPostfix(generatedContent);

    datapointRequest.content = generatedContent;
    let datapointMetadata;
    if (datapointRequest.metadata) {
      datapointMetadata = JSON.parse(datapointRequest.metadata);
      datapointMetadata.citation = generatedCitation;
      datapointMetadata = JSON.stringify(datapointMetadata);
    } else {
      datapointMetadata = JSON.stringify({
        citation: generatedCitation,
      });
    }
    const isAIEvaluator = await this.userService.userHasRequiredRole(userId, [
      USER_ROLES.SuperAdmin,
    ]);

    //Since we use the same function for both superadmin and non-superadmin users, we need to create these optional proerties
    const generatedDataResponse: {
      id: string;
      content: string;
      metadata: string;
    } = {
      content: generatedContent,
      metadata: datapointMetadata,
      id: datapointRequestId,
    };
    if (isAIEvaluator) {
      const dataGeneration = await this.datapointGenerationRepository.create({
        data: { content: generatedContent, metadata: datapointMetadata },
        datapointRequest: datapointRequest,
        evaluatorId: userId,
      });
      await this.datapointGenerationRepository.save(dataGeneration);
      generatedDataResponse.id = dataGeneration.id;
    } else {
      datapointRequest.metadata = datapointMetadata;
      datapointRequest.content = generatedContent;
      await this.datapointRequestRepository.save(datapointRequest);
    }

    //TODO: This should be passed as a success callback to the queue service
    await this.datapointStatusService.updateQueueStatus({
      datapointRequestId,
      queueStatus: null,
    });

    // comment this out if you don't find the preGapAnalysis useful
    //if (datapointGenerationChatCompletionResponse.response['preGapAnalysis']) {
    //  const globalAIUser:
    //    await this.userService.findGlobalGlacierAIUser();

    //  await this.projectService.addComment({
    //    commentableId: datapointRequestId,
    //    commentableType: CommentType.DatapointRequest,
    //    userId: globalAIUser.id,
    //    comment:
    //      datapointGenerationChatCompletionResponse.response['preGapAnalysis'],
    //    workspaceId,
    //  });
    //}

    this.logger.log(
      `Finished Generating datapointRequest ${datapointRequest.id} with AI`
    );

    const globalAIUser: User = await this.userService.findGlobalGlacierAIUser();

    await this.workspaceService.storeActionHistory({
      event: 'datapoint_request_ai_generated',
      ref: datapointRequestId,
      workspaceId: workspaceId,
      versionData: {
        event: isAIEvaluator
          ? 'datapoint_request_ai_generated'
          : 'datapoint_request_updated',
        doneBy: globalAIUser.id,
        issuedBy: userId,
        data: datapointRequest,
      },
    });
  }

  private async buildDatapointGenerationPrompts({
    datapointRequest,
    esrsDatapoint,
    materialTopicsInHierarchy,
    nonMaterialTopicsInHierarchy,
    useExistingReportTextForReference,
    relatedDatapoints,
    linkedChunksContext,
    otherDatapoints,
    generalCompanyProfile,
    companyDetails,
    testPrompts,
    dryRun,
    timingTracker,
  }: {
    datapointRequest: DatapointRequest;
    esrsDatapoint: ESRSDatapoint;
    materialTopicsInHierarchy: ESRSTopic[];
    nonMaterialTopicsInHierarchy: ESRSTopic[];
    useExistingReportTextForReference: boolean;
    relatedDatapoints: DatapointRequest[];
    linkedChunksContext: string;
    otherDatapoints: ESRSDatapoint[];
    generalCompanyProfile: string;
    companyDetails?: Company;
    testPrompts?: Record<
      string,
      { prompt: string; chainIdentifier: string; model?: LLM_MODELS }
    >;
    dryRun?: boolean;
    timingTracker?: TimingTracker;
  }): Promise<{
    prompts: ChatCompletionMessageParam[];
    promptIds: string[];
  }> {
    const h2Count = (datapointRequest.content.match(/<h2>/g) || []).length;
    const prompts: ChatCompletionMessageParam[] = [];
    const promptIds: string[] = [];

    // Determine the feature type using unified service
    const feature = this.promptProcessingUtilsService.determineFeatureType(
      esrsDatapoint,
      datapointRequest.content || '',
      'generation'
    );

    this.promptProcessingUtilsService.logFeatureType(
      feature,
      esrsDatapoint,
      h2Count
    );

    // Build unified variables object containing all data for A1-A6
    let unifiedVariables: Record<string, any> = {};

    // Base variables (formerly A1)
    if (
      feature === 'DP_MDRA_GENERATION' ||
      feature === 'DP_MDRP_GENERATION' ||
      feature === 'DP_MDRT_GENERATION'
    ) {
      unifiedVariables = {
        esrsDatapoint: {
          datapointId: esrsDatapoint.datapointId,
          name: esrsDatapoint.name,
          lawText: esrsDatapoint.lawText,
          footnotes: esrsDatapoint.footnotes,
          lawTextAR: esrsDatapoint.lawTextAR,
          footnotesAR: esrsDatapoint.footnotesAR,
          esrsDisclosureRequirement: {
            dr: esrsDatapoint.esrsDisclosureRequirement.dr,
          },
          companyName: companyDetails?.name || 'The company',
          isConditional: esrsDatapoint.conditional || false,
        },
        linkedChunksContext,
        language: datapointRequest.dataRequest.project.primaryContentLanguage,
        reportTextGenerationRules:
          datapointRequest.dataRequest.project.reportTextGenerationRules || '',
        generalCompanyProfile: generalCompanyProfile || {},
        reportingYear: datapointRequest.dataRequest.project.reportingYear || '',
        customUserRemark: datapointRequest.customUserRemark || '',
        currentYear: new Date().getFullYear(),
      };
    } else if (feature === 'DP_TABLE_GENERATION') {
      unifiedVariables = {
        esrsDatapoints: [
          {
            datapointId: esrsDatapoint.datapointId,
            name: esrsDatapoint.name,
            lawText: esrsDatapoint.lawText,
            footnotes: esrsDatapoint.footnotes,
            lawTextAR: esrsDatapoint.lawTextAR,
            footnotesAR: esrsDatapoint.footnotesAR,
            esrsDisclosureRequirement: {
              dr: esrsDatapoint.esrsDisclosureRequirement.dr,
            },
            isConditional: esrsDatapoint.conditional || false,
            exampleText: esrsDatapoint.exampleOutput || '',
          },
        ],
        generationLanguage:
          datapointRequest.dataRequest.project.primaryContentLanguage,
        reportTextGenerationRules:
          datapointRequest.dataRequest.project.reportTextGenerationRules || '',
        customUserRemark: datapointRequest.customUserRemark || '',
        linkedChunksContext,
        otherDatapoints: otherDatapoints.map((dp) => dp.datapointId),
        reportingYear: datapointRequest.dataRequest.project.reportingYear || '',
        generalCompanyProfile: generalCompanyProfile || {},
        currentYear: new Date().getFullYear(),
      };
    } else {
      // Numeric and Default share similar structure
      unifiedVariables = {
        esrsDatapoint: {
          datapointId: esrsDatapoint.datapointId,
          name: esrsDatapoint.name,
          lawText: esrsDatapoint.lawText,
          footnotes: esrsDatapoint.footnotes,
          lawTextAR: esrsDatapoint.lawTextAR,
          footnotesAR: esrsDatapoint.footnotesAR,
          esrsDisclosureRequirement: {
            dr: esrsDatapoint.esrsDisclosureRequirement.dr,
          },
          companyName: companyDetails?.name || 'The company',
          isConditional: esrsDatapoint.conditional || false,
        },
        generationLanguage:
          datapointRequest.dataRequest.project.primaryContentLanguage,
        reportTextGenerationRules:
          datapointRequest.dataRequest.project.reportTextGenerationRules || '',
        customUserRemark: datapointRequest.customUserRemark || '',
        generalCompanyProfile: generalCompanyProfile || {},
        otherDatapoints: otherDatapoints.map((dp) => dp.datapointId),
        reportingYear: datapointRequest.dataRequest.project.reportingYear || '',
        linkedChunksContext,
        currentYear: new Date().getFullYear(),
      };
    }

    // Add material topics (formerly A2)
    unifiedVariables.hasMaterialTopics = materialTopicsInHierarchy.length > 0;
    if (materialTopicsInHierarchy.length > 0) {
      const topicHierarchyLines = this.promptService.formatTopics({
        topics: materialTopicsInHierarchy,
        level: 0,
        material: true,
      });
      const mainMaterialTopics = materialTopicsInHierarchy
        .filter((t) => !t.parentId)
        .map((t) => t.name);
      unifiedVariables.materialTopics = topicHierarchyLines.join('\n');
      unifiedVariables.mainMaterialTopics = mainMaterialTopics.join(', ');
    }

    // Add non-material topics (formerly A3)
    unifiedVariables.hasNonMaterialTopics =
      nonMaterialTopicsInHierarchy.length > 0;
    if (nonMaterialTopicsInHierarchy.length > 0) {
      const topicHierarchyLines = this.promptService.formatTopics({
        topics: materialTopicsInHierarchy,
        level: 0,
        material: true,
      });
      unifiedVariables.nonMaterialTopics = topicHierarchyLines.join('\n');
    }

    // Add existing content (formerly A4)
    unifiedVariables.useExistingReportText =
      useExistingReportTextForReference && !!datapointRequest.content;
    if (useExistingReportTextForReference && datapointRequest.content) {
      unifiedVariables.existingContent = datapointRequest.content;
    }

    // Add related datapoints (formerly A6)
    unifiedVariables.hasRelatedDatapoints = relatedDatapoints.length > 0;
    if (relatedDatapoints.length > 0) {
      const relatedDatapointsSummary = relatedDatapoints.map(
        (dp) =>
          `${dp.esrsDatapoint.datapointId} - ${dp.esrsDatapoint.name}: ${dp.content ? 'Has content' : 'No content'}`
      );
      unifiedVariables.relatedDatapoints = relatedDatapointsSummary;
    }

    // Get the unified A1 prompt
    let unifiedPrompt: string;
    let a1PromptId: string | null = null;

    if (timingTracker) {
      timingTracker.start('Compile Unified Prompt A1');
    }

    if (dryRun && testPrompts && testPrompts['A1']) {
      // In dry run mode, compile the A1 prompt directly
      unifiedPrompt = await this.promptManagementService.compilePromptContent({
        promptContent: testPrompts['A1'].prompt,
        variables: unifiedVariables,
      });
    } else {
      // In normal mode, fetch from database and compile
      const basePrompt =
        await this.promptManagementService.findByFeatureAndChain(feature, 'A1');
      a1PromptId = basePrompt.id;
      unifiedPrompt = await this.promptManagementService.compilePromptContent({
        promptContent: basePrompt.prompt,
        variables: unifiedVariables,
      });
    }

    if (timingTracker) {
      timingTracker.end('Compile Unified Prompt A1');
    }

    prompts.push({
      role: 'system',
      content: unifiedPrompt,
    });

    // Track prompt ID if not in dry run mode
    if (!dryRun && a1PromptId) {
      promptIds.push(a1PromptId);
    }

    if (dryRun && testPrompts) {
      // In dry run mode, we don't have real prompt IDs
      return {
        prompts,
        promptIds: [],
      };
    }

    return {
      prompts,
      promptIds,
    };
  }

  async reviewDatapointContentWithControlledAI({
    datapointRequestId,
    userId,
    workspaceId,
    dryRun = false,
    testPrompts,
  }: {
    datapointRequestId: string;
    userId: string;
    workspaceId: string;
    dryRun?: boolean;
    testPrompts?: Record<
      string,
      { prompt: string; chainIdentifier: string; model: LLM_MODELS }
    >;
  }): Promise<
    | Comment[]
    | {
        gaps: any;
        metadata?: any;
        prompts: ChatCompletionMessageParam[];
        timings?: any;
      }
  > {
    // Initialize timing tracker for dry runs
    const timingTracker = dryRun ? new TimingTracker() : null;

    // Initialize array to track used prompt IDs
    const usedPromptIds: string[] = [];

    if (timingTracker) {
      timingTracker.start('Database Queries');
    }

    const datapointRequest = await this.datapointRequestRepository.findOne({
      where: {
        id: datapointRequestId,
        status: Not(DatapointRequestStatus.NotResponded),
      },
      relations: {
        dataRequest: { project: true },
        esrsDatapoint: {
          topicRelations: {
            topic: true,
          },
        },
        comments: true,
      },
    });

    if (timingTracker) {
      timingTracker.end('Database Queries');
    }

    const companyDetails =
      await this.workspaceService.getCompanyDetailFromWorkspaceId(workspaceId);
    const { generalCompanyProfile } = companyDetails;

    if (timingTracker) {
      timingTracker.start('Related Data Queries');
    }

    const otherDatapoints = await this.esrsDatapointRepository.find({
      where: {
        esrsDisclosureRequirementId:
          datapointRequest.esrsDatapoint.esrsDisclosureRequirementId,
        id: Not(datapointRequest.esrsDatapoint.id),
      },
    });

    // Get material and non-material topics
    const topicRelations = datapointRequest.esrsDatapoint.topicRelations;
    const materialTopicsInHierarchy =
      await this.materialTopicsService.generateHierarchicalListOfTopics({
        topicRelations,
        projectId: datapointRequest.dataRequest.project.id,
        material: true,
      });

    const nonMaterialTopicsInHierarchy =
      await this.materialTopicsService.generateHierarchicalListOfTopics({
        topicRelations,
        projectId: datapointRequest.dataRequest.project.id,
        material: false,
      });

    if (timingTracker) {
      timingTracker.end('Related Data Queries');
    }

    this.logger.log(
      `Start Gap Analysis for datapointRequest ${datapointRequest.id} with Controlled AI`
    );

    if (timingTracker) {
      timingTracker.start('Build Gap Analysis Prompts');
    }

    const { prompts, promptIds } = await this.buildGapAnalysisPrompts({
      datapointRequest,
      esrsDatapoint: datapointRequest.esrsDatapoint,
      materialTopicsInHierarchy,
      nonMaterialTopicsInHierarchy,
      otherDatapoints,
      generalCompanyProfile,
      companyDetails,
      testPrompts,
      dryRun,
      timingTracker,
    });

    // Add prompt IDs from buildGapAnalysisPrompts
    usedPromptIds.push(...promptIds);

    if (timingTracker) {
      timingTracker.end('Build Gap Analysis Prompts');
    }

    // Determine which model to use
    const h2Count = (datapointRequest.content.match(/<h2>/g) || []).length;
    const feature =
      datapointRequest.esrsDatapoint.datapointId.includes('MDR') && h2Count > 0
        ? 'DP_GAP_ANALYSIS_MDR'
        : 'DP_GAP_ANALYSIS_DEFAULT';

    // Determine which model to use for A1
    const finalModel = await this.promptProcessingUtilsService.getModelForChain(
      this.promptManagementService,
      feature,
      'A1',
      testPrompts,
      dryRun
    );

    if (timingTracker) {
      timingTracker.start('A1 LLM Call');
    }

    let gapAnalysisResponse;
    try {
      gapAnalysisResponse = await this.llmRateLimiterService.handleRequest({
        model: finalModel,
        messages: prompts,
        json: true,
        temperature: 0.3,
      });
    } catch (error) {
      this.logger.error(`Error in A1 LLM call: ${error.message}`);
      throw error;
    }

    if (timingTracker) {
      timingTracker.end('A1 LLM Call');
    }

    if (gapAnalysisResponse.status === 400) {
      this.logger.error(
        `Error in gap analysis: ${gapAnalysisResponse.response}`
      );
      throw new Error(`Gap analysis failed: ${gapAnalysisResponse.response}`);
    }

    let finalGapAnalysisResponse;

    // Check if we need B1 formatting prompt
    let b1Prompt: string;
    let b1PromptId: string | null = null;

    if (gapAnalysisResponse.response.gapIdentified === false) {
      // If no gap identified, return A1 response directly
      this.logger.warn(
        'No gap identified in A1 response, skipping B1 formatting'
      );
      finalGapAnalysisResponse = gapAnalysisResponse;
    } else if (dryRun && testPrompts && testPrompts['B1']) {
      b1Prompt = await this.promptManagementService.compilePromptContent({
        promptContent: testPrompts['B1'].prompt,
        variables: {
          previousResponse: JSON.stringify(gapAnalysisResponse.response),
          language: datapointRequest.dataRequest.project.primaryContentLanguage,
        },
      });
    } else {
      try {
        const formattingPrompt =
          await this.promptManagementService.findByFeatureAndChain(
            feature,
            'B1'
          );
        b1PromptId = formattingPrompt.id;
        b1Prompt = await this.promptManagementService.compilePromptContent({
          promptContent: formattingPrompt.prompt,
          variables: {
            previousResponse: JSON.stringify(gapAnalysisResponse.response),
            language:
              datapointRequest.dataRequest.project.primaryContentLanguage,
          },
        });
      } catch (error) {
        // If B1 prompt doesn't exist, use the A1 response directly
        this.logger.warn(
          'B1 formatting prompt not found, using A1 response directly'
        );
        finalGapAnalysisResponse = gapAnalysisResponse;
      }
    }

    // Track B1 prompt ID if not in dry run mode
    if (!dryRun && b1PromptId) {
      usedPromptIds.push(b1PromptId);
    }

    if (b1Prompt) {
      const formattingPrompts: ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: b1Prompt,
        },
      ];

      if (timingTracker) {
        timingTracker.start('B1 LLM Call');
      }

      // Determine model for B1 call
      const b1Model = await this.promptProcessingUtilsService.getModelForChain(
        this.promptManagementService,
        feature,
        'B1',
        testPrompts,
        dryRun,
        finalModel
      );

      try {
        finalGapAnalysisResponse =
          await this.llmRateLimiterService.handleRequest({
            model: b1Model,
            messages: formattingPrompts,
            json: true,
            temperature: 0.3,
          });
      } catch (error) {
        this.logger.error(`Error in B1 LLM call: ${error.message}`);
        // Fall back to A1 response
        finalGapAnalysisResponse = gapAnalysisResponse;
      }

      if (timingTracker) {
        timingTracker.end('B1 LLM Call');
      }
    } else {
      finalGapAnalysisResponse = gapAnalysisResponse;
    }

    if (timingTracker) {
      timingTracker.start('Process Response');
    }

    let gapAnalysisRaw = JSON.stringify(finalGapAnalysisResponse.response);
    // replace ‹ and › with < and > for eac
    gapAnalysisRaw = gapAnalysisRaw.replace(/‹/g, '<').replace(/›/g, '>');
    const gapAnalysis = JSON.parse(gapAnalysisRaw);

    // If this is a dry run, return the analysis without saving
    if (dryRun) {
      if (timingTracker) {
        timingTracker.end('Process Response');
      }

      return {
        gaps: gapAnalysis,
        metadata: {
          usedPromptIds,
          tokens: finalGapAnalysisResponse.token,
        },
        prompts,
        timings:
          this.promptProcessingUtilsService.extractTimings(timingTracker),
      };
    }

    // Save comments in production mode
    const globalAIUser: User = await this.userService.findGlobalGlacierAIUser();
    const isAIEvaluator = await this.userService.userHasRequiredRole(userId, [
      USER_ROLES.SuperAdmin,
    ]);
    const comments: Comment[] = [];
    let gapHtml = '';

    type GapInformation = {
      gap?: string;
      actions?: string[];
      exampleText?: string;
      text?: string;
      disclaimer?: string;
      title?: string;
    };

    const typedGapAnalysis: GapInformation & {
      gapIdentified: boolean;
      gaps?: GapInformation[];
    } = gapAnalysis;

    if (
      typedGapAnalysis.gapIdentified &&
      typedGapAnalysis.gaps &&
      typedGapAnalysis.gaps.length > 0
    ) {
      gapHtml = `<h2>Gap Analysis Results</h2>`;
      typedGapAnalysis.gaps.forEach((gap) => {
        if (gap.title) {
          gapHtml += `<h3>${gap.title}</h3>`;
        }
        if (gap.gap) {
          gapHtml += `<p><strong>Gap Identified:</strong> ${gap.gap}</p>`;
        }
        if (gap.actions && gap.actions.length > 0) {
          gapHtml += `<p><strong>Recommended Actions:</strong></p><ul>`;
          gap.actions.forEach((action) => {
            gapHtml += `<li>${action}</li>`;
          });
          gapHtml += `</ul>`;
        }
        if (gap.exampleText) {
          gapHtml += `<p><strong>Example Text:</strong> ${gap.exampleText}</p>`;
        }
      });
      if (typedGapAnalysis.disclaimer) {
        gapHtml += `<p><em>${typedGapAnalysis.disclaimer}</em></p>`;
      }
    } else if (typedGapAnalysis.gapIdentified) {
      gapHtml = `<h2>Gap Analysis Results</h2>`;
      if (typedGapAnalysis.gap) {
        gapHtml += `<p><strong>Gap Identified:</strong> ${typedGapAnalysis.gap}</p>`;
      }
      if (typedGapAnalysis.actions && typedGapAnalysis.actions.length > 0) {
        gapHtml += `<p><strong>Recommended Actions:</strong></p><ul>`;
        typedGapAnalysis.actions.forEach((action) => {
          gapHtml += `<li>${action}</li>`;
        });
        gapHtml += `</ul>`;
      }
      if (typedGapAnalysis.exampleText) {
        gapHtml += `<p><strong>Example Text:</strong> ${typedGapAnalysis.exampleText}</p>`;
      }
      if (typedGapAnalysis.disclaimer) {
        gapHtml += `<p><em>${typedGapAnalysis.disclaimer}</em></p>`;
      }
    } else {
      gapHtml = `<h2>Gap Analysis Results</h2><p>No significant gaps were identified in the current content.</p>`;
      if (typedGapAnalysis.disclaimer) {
        gapHtml += `<p><em>${typedGapAnalysis.disclaimer}</em></p>`;
      }
    }

    const comment = await this.projectService.addComment({
      commentableId: datapointRequestId,
      commentableType: CommentType.DatapointRequest,
      userId: globalAIUser.id,
      comment: gapHtml,
      workspaceId,
      evaluationLot: isAIEvaluator,
    });

    comments.push(comment);

    if (datapointRequest.queueStatus === DatapointQueueStatus.QueuedForReview) {
      await this.datapointStatusService.updateQueueStatus({
        datapointRequestId,
        queueStatus: null,
      });
    }

    if (timingTracker) {
      timingTracker.end('Process Response');
    }

    this.logger.log(
      `Finished Gap Analysis for datapointRequest ${datapointRequest.id} with Controlled AI`
    );

    return comments;
  }

  private async buildGapAnalysisPrompts({
    datapointRequest,
    esrsDatapoint,
    materialTopicsInHierarchy,
    nonMaterialTopicsInHierarchy,
    otherDatapoints,
    generalCompanyProfile,
    companyDetails,
    testPrompts,
    dryRun,
    timingTracker,
  }: {
    datapointRequest: DatapointRequest;
    esrsDatapoint: ESRSDatapoint;
    materialTopicsInHierarchy: ESRSTopic[];
    nonMaterialTopicsInHierarchy: ESRSTopic[];
    otherDatapoints: ESRSDatapoint[];
    generalCompanyProfile: string;
    companyDetails?: Company;
    testPrompts?: Record<
      string,
      { prompt: string; chainIdentifier: string; model?: LLM_MODELS }
    >;
    dryRun?: boolean;
    timingTracker?: TimingTracker;
  }): Promise<{
    prompts: ChatCompletionMessageParam[];
    promptIds: string[];
  }> {
    const prompts: ChatCompletionMessageParam[] = [];
    const promptIds: string[] = [];

    // Determine the feature type for gap analysis
    const feature = this.promptProcessingUtilsService.determineFeatureType(
      esrsDatapoint,
      datapointRequest.content || '',
      'gap-analysis'
    );

    this.promptProcessingUtilsService.logFeatureType(
      feature,
      esrsDatapoint,
      (datapointRequest.content.match(/<h2>/g) || []).length
    );

    // Build unified variables object for gap analysis
    const baseVariables =
      this.promptProcessingUtilsService.buildUnifiedVariables(
        datapointRequest,
        esrsDatapoint,
        materialTopicsInHierarchy,
        nonMaterialTopicsInHierarchy,
        generalCompanyProfile,
        companyDetails,
        {
          content: datapointRequest.content || '',
          h2Count: (datapointRequest.content.match(/<h2>/g) || []).length,
          isConditional: esrsDatapoint.conditional || false,
          otherDatapoints: otherDatapoints.map((dp) => ({
            datapointId: dp.datapointId,
            name: dp.name,
          })),
        }
      );

    // Get the unified A1 prompt
    let unifiedPrompt: string;
    let a1PromptId: string | null = null;

    if (timingTracker) {
      timingTracker.start('Compile Unified Gap Analysis Prompt A1');
    }

    if (dryRun && testPrompts && testPrompts['A1']) {
      // In dry run mode, compile the A1 prompt directly
      unifiedPrompt = await this.promptManagementService.compilePromptContent({
        promptContent: testPrompts['A1'].prompt,
        variables: baseVariables,
      });
    } else {
      // In normal mode, fetch from database and compile
      const basePrompt =
        await this.promptManagementService.findByFeatureAndChain(feature, 'A1');
      a1PromptId = basePrompt.id;
      unifiedPrompt = await this.promptManagementService.compilePromptContent({
        promptContent: basePrompt.prompt,
        variables: baseVariables,
      });
    }

    if (timingTracker) {
      timingTracker.end('Compile Unified Gap Analysis Prompt A1');
    }

    prompts.push({
      role: 'system',
      content: unifiedPrompt,
    });

    // Track prompt ID if not in dry run mode
    if (!dryRun && a1PromptId) {
      promptIds.push(a1PromptId);
    }

    if (dryRun && testPrompts) {
      // In dry run mode, we don't have real prompt IDs
      return {
        prompts,
        promptIds: [],
      };
    }

    return {
      prompts,
      promptIds,
    };
  }
}
