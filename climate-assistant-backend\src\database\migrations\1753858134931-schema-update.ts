import { MigrationInterface, QueryRunner } from 'typeorm';
import { SystemPermissions } from '../../constants';

/**
 * Migration to add datapoint assignment functionality
 *
 * This migration:
 * 1. Updates the permission_name_enum to include ASSIGN_USERS_DATAPOINTS permission
 * 2. Adds responsiblePersonId column to datapoint_request table for user assignment
 * 3. Creates foreign key constraint between datapoint_request and user tables
 * 4. Inserts the new ASSIGN_USERS_DATAPOINTS permission into the permission table
 * 5. Assigns the new permission to SUPER_ADMIN and WORKSPACE_ADMIN roles
 */
export class AddDatapointAssignment1753858134931 implements MigrationInterface {
  name = 'AddDatapointAssignmentPermissions1753858134931';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, update the permission enum to include ASSIGN_USERS_DATAPOINTS
    const allPermissions = Object.values(SystemPermissions);
    const enumValues = allPermissions.map((value) => `'${value}'`).join(', ');

    // Drop and recreate the enum with new values
    await queryRunner.query(
      `ALTER TYPE "public"."permission_name_enum" RENAME TO "permission_name_enum_old"`
    );

    await queryRunner.query(
      `CREATE TYPE "public"."permission_name_enum" AS ENUM (${enumValues})`
    );

    await queryRunner.query(
      `ALTER TABLE "permission" ALTER COLUMN "name" TYPE "public"."permission_name_enum" USING "name"::text::"public"."permission_name_enum"`
    );

    await queryRunner.query(`DROP TYPE "public"."permission_name_enum_old"`);

    // Add responsiblePersonId column to datapoint_request table
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" ADD COLUMN IF NOT EXISTS "responsiblePersonId" uuid`
    );

    // Add foreign key constraint between datapoint_request and user
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" ADD CONSTRAINT "FK_744a3ccc48b6d54cae19418bed8" FOREIGN KEY ("responsiblePersonId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );

    // Insert the ASSIGN_USERS_DATAPOINTS permission if it doesn't exist
    await queryRunner.query(
      `
          INSERT INTO "permission" ("name", "description")
          VALUES ($1, $2)
          ON CONFLICT ("name") DO NOTHING
          `,
      [SystemPermissions.ASSIGN_USERS_DATAPOINTS, 'Assign users to datapoints']
    );

    // Add ASSIGN_USERS_DATAPOINTS permission to SUPER_ADMIN and WORKSPACE_ADMIN role
    await queryRunner.query(
      `
          WITH role_data AS (
            SELECT id FROM "role" WHERE name IN ('WORKSPACE_ADMIN', 'SUPER_ADMIN')
          ),
          permission_data AS (
            SELECT id FROM "permission" WHERE name = $1
          )
          INSERT INTO "role_permission" ("roleId", "permissionId")
          SELECT rd.id, pd.id
          FROM role_data rd
          CROSS JOIN permission_data pd
          ON CONFLICT ("roleId", "permissionId") DO NOTHING
          `,
      [SystemPermissions.ASSIGN_USERS_DATAPOINTS]
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove responsiblePersonId column from datapoint_request table
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" DROP COLUMN "responsiblePersonId"`
    );

    // Drop foreign key constraint between datapoint_request and user
    await queryRunner.query(
      `ALTER TABLE "datapoint_request" DROP CONSTRAINT "FK_744a3ccc48b6d54cae19418bed8"`
    );

    // Remove ASSIGN_USERS_DATAPOINTS permission from SUPER_ADMIN and WORKSPACE_ADMIN role
    await queryRunner.query(
      `
      DELETE FROM "role_permission"
      WHERE "roleId" IN (SELECT id FROM "role" WHERE name IN ('WORKSPACE_ADMIN', 'SUPER_ADMIN'))
      AND "permissionId" IN (SELECT id FROM "permission" WHERE name = $1)
      `,
      [SystemPermissions.ASSIGN_USERS_DATAPOINTS]
    );

    // Remove the ASSIGN_USERS_DATAPOINTS permission
    await queryRunner.query(
      `
      DELETE FROM "permission"
      WHERE name = $1
      `,
      [SystemPermissions.ASSIGN_USERS_DATAPOINTS]
    );

    // Update the permission enum to remove ASSIGN_USERS_DATAPOINTS
    const allPermissions = Object.values(SystemPermissions).filter(
      (perm) => perm !== SystemPermissions.ASSIGN_USERS_DATAPOINTS
    );
    const enumValues = allPermissions.map((value) => `'${value}'`).join(', ');

    // Drop and recreate the enum without ASSIGN_USERS_DATAPOINTS
    await queryRunner.query(
      `ALTER TYPE "public"."permission_name_enum" RENAME TO "permission_name_enum_old"`
    );

    await queryRunner.query(
      `CREATE TYPE "public"."permission_name_enum" AS ENUM (${enumValues})`
    );

    await queryRunner.query(
      `ALTER TABLE "permission" ALTER COLUMN "name" TYPE "public"."permission_name_enum" USING "name"::text::"public"."permission_name_enum"`
    );

    await queryRunner.query(`DROP TYPE "public"."permission_name_enum_old"`);
  }
}
