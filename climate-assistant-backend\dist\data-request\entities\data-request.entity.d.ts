import { ESRSDisclosureRequirement } from '../../esrs/entities/esrs-disclosure-requirement.entity';
import { User } from '../../users/entities/user.entity';
import { Project } from '../../project/entities/project.entity';
import { DatapointRequest } from '../../datapoint/entities/datapoint-request.entity';
import { Comment } from '../../project/entities/comment.entity';
import { CommentGeneration } from '../../project/entities/comment-generation.entity';
import { DataRequestGeneration } from './datarequest-generation.entity';
export declare enum DataRequestStatus {
    IncompleteData = "incomplete_data",
    CompleteData = "complete_data",
    NotReported = "not_reported"
}
export declare enum DataRequestQueueStatus {
    QueuedForGeneration = "queued_for_generation",
    QueuedForReview = "queued_for_review"
}
export declare class DataRequest {
    id: string;
    dataRequestTypeId: number;
    dataRequestType: string;
    status: DataRequestStatus;
    content: string;
    customUserRemark: string;
    dueDate: Date;
    responsiblePersonId: string;
    approvedBy: string;
    approvedAt: Date;
    queueStatus: DataRequestQueueStatus;
    createdAt: Date;
    updatedAt: Date;
    projectId: string;
    content_version: string;
    dataRequestGenerations: DataRequestGeneration[];
    disclosureRequirement: ESRSDisclosureRequirement;
    responsiblePerson: User;
    approver: User;
    project: Project;
    datapointRequests: DatapointRequest[];
    comments: Comment[];
    commentGenerations: CommentGeneration[];
}
