"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const project_entity_1 = require("./entities/project.entity");
const comment_entity_1 = require("./entities/comment.entity");
const data_request_entity_1 = require("../data-request/entities/data-request.entity");
const esrs_service_1 = require("../esrs/esrs.service");
const user_entity_1 = require("../users/entities/user.entity");
const workspace_service_1 = require("../workspace/workspace.service");
const prompts_service_1 = require("../prompts/prompts.service");
const htmlDocx = require("html-docx-js");
const marked_1 = require("marked");
const material_esrs_topic_entity_1 = require("./entities/material-esrs-topic.entity");
const datapoint_request_entity_1 = require("../datapoint/entities/datapoint-request.entity");
const esrs_topic_datapoint_entity_1 = require("../esrs/entities/esrs-topic-datapoint.entity");
const esrs_topic_entity_1 = require("../esrs/entities/esrs-topic.entity");
const comment_generation_entity_1 = require("./entities/comment-generation.entity");
const config_1 = require("../util/config");
const constants_1 = require("../constants");
const llm_rate_limiter_service_1 = require("../llm/services/llm-rate-limiter.service");
const export_project_excel_1 = require("./helpers/export-project-excel");
const common_util_1 = require("../util/common-util");
const export_dr_gaps_excel_1 = require("./helpers/export-dr-gaps-excel");
let ProjectService = class ProjectService {
    constructor(userRepository, projectRepository, commentRepository, commentGenerationRepository, dataRequestRepository, materialESRSTopicRepository, esrsTopicDatapointRepository, datapointRequestRepository, esrsService, workspaceService, llmRateLimitService, promptService, dataSource) {
        this.userRepository = userRepository;
        this.projectRepository = projectRepository;
        this.commentRepository = commentRepository;
        this.commentGenerationRepository = commentGenerationRepository;
        this.dataRequestRepository = dataRequestRepository;
        this.materialESRSTopicRepository = materialESRSTopicRepository;
        this.esrsTopicDatapointRepository = esrsTopicDatapointRepository;
        this.datapointRequestRepository = datapointRequestRepository;
        this.esrsService = esrsService;
        this.workspaceService = workspaceService;
        this.llmRateLimitService = llmRateLimitService;
        this.promptService = promptService;
        this.dataSource = dataSource;
    }
    async findAll(workspaceId) {
        return this.projectRepository.find({
            where: { workspaceId },
        });
    }
    async findActiveCSRDProjectByUser(userId) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
            relations: {
                userWorkspaces: {
                    workspace: {
                        projects: true,
                    },
                },
            },
        });
        if (user.userWorkspaces && user.userWorkspaces.length > 0) {
            const workspace = user.userWorkspaces[0].workspace;
            if (workspace.projects && workspace.projects.length > 0) {
                return workspace.projects[0];
            }
        }
        return null;
    }
    async create({ workspaceId, userId, createProjectRequest, }) {
        const project = this.projectRepository.create({
            name: createProjectRequest.name,
            primaryContentLanguage: createProjectRequest.primaryContentLanguage,
            workspaceId,
            createdBy: userId,
        });
        const store = await this.projectRepository.save(project);
        if (createProjectRequest.type === 'CSRD') {
            await this.initializeCSRDProjectData(store.id);
        }
        return store;
    }
    async initializeCSRDProjectData(projectId) {
        await this.dataSource.transaction(async (entityManager) => {
            await entityManager.query(`
          INSERT INTO data_request("dataRequestTypeId", "dataRequestType", "status", "content", "projectId")
          SELECT "id" as "dataRequestTypeId", 'ESRS' as "dataRequestType", 'incomplete_data' as "status", '' as "content", $1 as "projectId" 
          FROM esrs_disclosure_requirement "dr1" 
          WHERE "id" NOT IN (
              SELECT "dr2"."dataRequestTypeId" as "id" 
              FROM "data_request" "dr2" 
              WHERE "dr2"."projectId" = $1
          )
        `, [projectId]);
            await entityManager.query(`
          INSERT INTO datapoint_request("esrsDatapointId", "dataRequestId", "status", "content")
          SELECT "esrs_datapoint"."id" as "esrsDatapointId", "dr"."id" as "dataRequestId", 'incomplete_data' as "status", '' as "content"
          FROM "data_request" "dr" 
          JOIN "esrs_disclosure_requirement" "edr" ON "edr"."id" = "dr"."dataRequestTypeId" 
          JOIN "esrs_datapoint" ON "esrs_datapoint"."esrsDisclosureRequirementId" = "edr"."id"
          WHERE "dr"."projectId" = $1
          AND "esrs_datapoint"."id" NOT IN (
              SELECT "dpr2"."esrsDatapointId" as "id" 
              FROM "datapoint_request" "dpr2" 
              JOIN "data_request" "dr2" ON "dpr2"."dataRequestId" = "dr2"."id" 
              WHERE "dr2"."projectId" = $1
          )
        `, [projectId]);
            await entityManager.query(`
          UPDATE datapoint_request 
          SET "status" = 'not_reported' 
          FROM esrs_datapoint "edp", data_request "dr" 
          WHERE "dr"."id" = datapoint_request."dataRequestId" 
          AND "edp"."id" = datapoint_request."esrsDatapointId" 
          AND "edp"."optional" = true
          AND "dr"."projectId" = $1
        `, [projectId]);
        });
    }
    async findById(projectId) {
        const project = await this.projectRepository.findOne({
            where: { id: projectId },
        });
        if (!project) {
            throw new common_1.NotFoundException(`Project with ID ${projectId} not found`);
        }
        return project;
    }
    async findData(projectId) {
        const project = await this.projectRepository
            .createQueryBuilder('project')
            .leftJoinAndSelect('project.workspace', 'workspace')
            .leftJoinAndSelect('project.creator', 'creator')
            .leftJoinAndSelect('project.materialTopics', 'materialTopics')
            .leftJoinAndSelect('project.dataRequests', 'dataRequests')
            .leftJoinAndSelect('dataRequests.responsiblePerson', 'responsiblePerson')
            .leftJoinAndSelect('dataRequests.disclosureRequirement', 'disclosureRequirement')
            .where('project.id = :projectId', { projectId })
            .select([
            'project.id',
            'project.workspaceId',
            'project.name',
            'project.primaryContentLanguage',
            'project.reportTextGenerationRules',
            'project.reportingYear',
            'project.createdBy',
            'project.createdAt',
            'dataRequests.id',
            'dataRequests.status',
            'dataRequests.dueDate',
            'responsiblePerson.name',
            'responsiblePerson.email',
            'disclosureRequirement.dr',
            'disclosureRequirement.esrs',
            'disclosureRequirement.name',
            'disclosureRequirement.sort',
        ])
            .getOne();
        if (!project) {
            throw new common_1.NotFoundException(`Project with ID ${projectId} not found`);
        }
        const datapointStats = await this.datapointRequestRepository
            .createQueryBuilder('dp')
            .select('dp.dataRequestId', 'dataRequestId')
            .addSelect('dp.status', 'status')
            .addSelect('COUNT(*)', 'count')
            .innerJoin('dp.dataRequest', 'dr')
            .where('dr.projectId = :projectId', { projectId })
            .groupBy('dp.dataRequestId')
            .addGroupBy('dp.status')
            .getRawMany();
        const statsMap = new Map();
        datapointStats.forEach((stat) => {
            if (!statsMap.has(stat.dataRequestId)) {
                statsMap.set(stat.dataRequestId, {
                    total: 0,
                    completed: 0,
                    notReported: 0,
                });
            }
            const stats = statsMap.get(stat.dataRequestId);
            stats.total += parseInt(stat.count);
            if (stat.status === datapoint_request_entity_1.DatapointRequestStatus.CompleteData) {
                stats.completed = parseInt(stat.count);
            }
            else if (stat.status === datapoint_request_entity_1.DatapointRequestStatus.NotResponded) {
                stats.notReported = parseInt(stat.count);
            }
        });
        project.dataRequests.forEach((dr) => {
            const stats = statsMap.get(dr.id) || {
                total: 0,
                completed: 0,
                notReported: 0,
            };
            dr.datapointStats = {
                totalFiltered: stats.total - stats.notReported,
                completed: stats.completed,
                progressPercentage: stats.total - stats.notReported > 0
                    ? Math.round((stats.completed / (stats.total - stats.notReported)) * 100)
                    : 0,
            };
        });
        return project;
    }
    async update({ projectId, workspaceId, createdBy, updateProjectRequest, }) {
        const project = await this.findById(projectId);
        if (!project) {
            throw new common_1.NotFoundException(`Project not found`);
        }
        if (workspaceId !== project.workspaceId) {
            throw new common_1.UnauthorizedException(`Project is not from this workspace`);
        }
        if (updateProjectRequest.reportTextGenerationRules &&
            updateProjectRequest.reportTextGenerationRules.length > 3) {
            const validate = await this.validateReportTextGenerationRules(updateProjectRequest.reportTextGenerationRules, updateProjectRequest.primaryContentLanguage);
            if (validate.status === 403) {
                throw new common_1.ForbiddenException(validate.reason);
            }
        }
        Object.assign(project, updateProjectRequest);
        const store = await this.projectRepository.save(project);
        await this.workspaceService.storeActionHistory({
            event: 'project_updated',
            ref: projectId,
            workspaceId: workspaceId,
            versionData: {
                event: 'project_updated',
                doneBy: createdBy,
                data: store,
            },
        });
        return store;
    }
    async delete({ projectId, workspaceId, userId, }) {
        const project = await this.findById(projectId);
        if (!project) {
            throw new common_1.NotFoundException(`Project not found`);
        }
        if (workspaceId !== project.workspaceId) {
            throw new common_1.UnauthorizedException(`Project is not from this workspace`);
        }
        await this.projectRepository.remove(project);
    }
    async findComment({ commentId }) {
        const commentData = await this.commentRepository.findOne({
            where: { id: commentId },
            relations: ['user'],
        });
        if (!commentData) {
            throw new common_1.NotFoundException(`Comment not found`);
        }
        return commentData;
    }
    async addComment({ commentableId, commentableType, userId, workspaceId, comment, evaluationLot, }) {
        if (evaluationLot) {
            const storeComment = this.commentGenerationRepository.create({
                commentableId: commentableId,
                commentableType: commentableType,
                userId,
                comment,
                status: comment_generation_entity_1.CommentStatus.Pending,
            });
            await this.commentGenerationRepository.save(storeComment);
            await this.workspaceService.storeActionHistory({
                event: commentableType + '_comment_gen_created',
                ref: commentableId,
                workspaceId: workspaceId,
                versionData: {
                    event: 'comment_gen_created',
                    doneBy: userId,
                    data: storeComment,
                },
            });
        }
        else {
            const storeComment = this.commentRepository.create({
                commentableId: commentableId,
                commentableType: commentableType,
                userId,
                comment,
                resolved: false,
            });
            await this.commentRepository.save(storeComment);
            await this.workspaceService.storeActionHistory({
                event: commentableType + '_comment_created',
                ref: commentableId,
                workspaceId: workspaceId,
                versionData: {
                    event: 'comment_created',
                    doneBy: userId,
                    data: storeComment,
                },
            });
            return await this.findComment({ commentId: storeComment.id });
        }
    }
    async updateComment({ commentId, userId, workspaceId, comment, }) {
        const commentData = await this.commentRepository.findOne({
            where: { id: commentId },
        });
        if (!commentData) {
            throw new common_1.NotFoundException(`Comment not found`);
        }
        commentData.userId = userId;
        commentData.comment = comment;
        await this.commentRepository.save(commentData);
        await this.workspaceService.storeActionHistory({
            event: commentData.commentableType + '_comment_updated',
            ref: commentId,
            workspaceId: workspaceId,
            versionData: {
                event: 'comment_updated',
                doneBy: userId,
                data: commentData,
            },
        });
        return commentData;
    }
    async updateCommentGenerationStatus({ commentId, userId, workspaceId, data, }) {
        const commentData = await this.commentGenerationRepository.findOne({
            where: { id: commentId },
        });
        if (!commentData) {
            throw new common_1.NotFoundException(`Comment not found`);
        }
        if (data.status === comment_generation_entity_1.CommentStatus.Approved) {
            await this.addComment({
                commentableId: commentData.commentableId,
                commentableType: commentData.commentableType,
                userId: commentData.userId,
                workspaceId: workspaceId,
                comment: commentData.comment,
                evaluationLot: false,
            });
        }
        commentData.status = data.status;
        commentData.evaluatorComment = data.evaluatorComment;
        commentData.evaluatorId = userId;
        commentData.evaluatedAt = new Date();
        await this.commentGenerationRepository.save(commentData);
        return await this.commentGenerationRepository.findOne({
            where: { id: commentId },
        });
    }
    async deleteComment({ commentId, userId, workspaceId, }) {
        const commentData = await this.commentRepository.findOne({
            where: { id: commentId },
        });
        const user = await this.userRepository.findOne({
            where: { id: userId },
            relations: ['userWorkspaces', 'userWorkspaces.role'],
        });
        if (!commentData) {
            throw new common_1.NotFoundException(`Comment not found`);
        }
        await this.workspaceService.storeActionHistory({
            event: commentData.commentableType + '_comment_deleted',
            ref: commentId,
            workspaceId: workspaceId,
            versionData: {
                event: 'comment_deleted',
                doneBy: userId,
                data: commentData,
            },
        });
        await this.commentRepository.remove(commentData);
    }
    async resolveComment({ commentId, resolution, }) {
        const commentData = await this.commentRepository.findOne({
            where: { id: commentId },
        });
        if (!commentData) {
            throw new common_1.NotFoundException(`Comment not found`);
        }
        commentData.resolved = resolution;
        await this.commentRepository.save(commentData);
        return await this.findComment({ commentId });
    }
    async findAssignedESRSDatapoints({ esrs, workspaceId, }) {
        const project = await this.projectRepository.findOne({
            where: { workspaceId },
        });
        const drs = await this.dataRequestRepository.find({
            where: { projectId: project.id },
            relations: ['datapointRequests'],
        });
        const dprs = drs.map((dr) => dr.datapointRequests).flat();
        const esrsDatapoints = await this.esrsService.getEsrsDatapointsByStandard(esrs);
        const filteredEsrsDatapoints = esrsDatapoints
            .map((esrsDatapoint) => {
            return {
                ...esrsDatapoint,
                datapointRequestId: dprs.find((dpr) => dpr.esrsDatapointId === esrsDatapoint.id)?.id,
            };
        })
            .filter((esrsDatapoint) => {
            return esrsDatapoint.datapointRequestId !== undefined;
        });
        return filteredEsrsDatapoints;
    }
    async validateReportTextGenerationRules(text, language = project_entity_1.Language.EN) {
        const datapointGenerationChatCompletion = [
            {
                role: 'system',
                content: this.promptService.validateReportTextGenerationRulesPrompt(language),
            },
            {
                role: 'user',
                content: text,
            },
        ];
        const datapointGenerationChatCompletionResponse = await this.llmRateLimitService.handleRequest({
            model: constants_1.LLM_MODELS['gpt-4o'],
            messages: datapointGenerationChatCompletion,
            json: true,
            temperature: 0.5,
        });
        if (datapointGenerationChatCompletionResponse.status === 400) {
            return {
                status: 403,
                reason: datapointGenerationChatCompletionResponse.response,
            };
        }
        return datapointGenerationChatCompletionResponse.response;
    }
    async generateDocx(projectId) {
        const dataRequests = await this.dataRequestRepository.find({
            where: { projectId, status: data_request_entity_1.DataRequestStatus.CompleteData },
            relations: ['disclosureRequirement'],
        });
        if (dataRequests.length === 0) {
            throw new common_1.NotFoundException('No approved reporttexts found for this project');
        }
        const markdownContent = dataRequests
            .sort((a, b) => {
            const esrsSortOrderA = config_1.ESRS_SORT_ORDER[a.disclosureRequirement.esrs] || 999;
            const esrsSortOrderB = config_1.ESRS_SORT_ORDER[b.disclosureRequirement.esrs] || 999;
            if (esrsSortOrderA !== esrsSortOrderB) {
                return esrsSortOrderA - esrsSortOrderB;
            }
            return a.disclosureRequirement.sort - b.disclosureRequirement.sort;
        })
            .map((dataRequest) => dataRequest.content)
            .join('\n\n');
        const htmlContent = '<meta charset="UTF-8">' + (await (0, marked_1.marked)(markdownContent));
        const docxBlob = htmlDocx.asBlob(htmlContent);
        const arrayBuffer = await docxBlob.arrayBuffer();
        const docxBuffer = Buffer.from(arrayBuffer);
        return docxBuffer;
    }
    async generateXlsx(projectId) {
        const datapoints = await this.dataRequestRepository.query(`
          SELECT
            edr.esrs,
            edr.dr,
            edp."datapointId",
            edp.name as "datapoint",
            edp."dataType",
            edp.paragraph as "paragraph",
            edp."relatedAR" as "ar",
            dpr.content as "datapointText",
            dpc.comment AS "datapointGaps"
          FROM data_request dr
            JOIN esrs_disclosure_requirement edr ON dr."dataRequestTypeId" = edr.id
            JOIN datapoint_request dpr ON dr.id = dpr."dataRequestId"
            JOIN esrs_datapoint edp ON dpr."esrsDatapointId" = edp.id
            LEFT JOIN comment drc ON dr.id = drc."commentableId" AND drc."commentableType" = 'data_request' AND drc.resolved = false
            LEFT JOIN comment dpc ON dpr.id = dpc."commentableId" and dpc."commentableType" = 'datapoint_request' AND dpc.resolved = false
          WHERE dr."projectId" = $1 AND dpr.content IS NOT NULL AND dpr.content != ''
          ORDER BY edp."datapointId"
        `, [projectId]);
        const xlsBuffer = await (0, export_project_excel_1.generateExcelReport)(datapoints);
        return xlsBuffer;
    }
    async generateDrGapsXlsx(projectId) {
        const drGaps = await this.dataRequestRepository.query(`
          SELECT
            edr.esrs,
            edr.dr as "drId",
            edr.name as "drTitle",
            dr.content as "drText",
            edr."drDescription" as "drDescription",
            edr."drObjective",
            edr."lawText",
            edr."lawTextAR",
            edr.sort,
            drc.comment AS "drGaps"
          FROM data_request dr
            JOIN esrs_disclosure_requirement edr ON dr."dataRequestTypeId" = edr.id
            LEFT JOIN comment drc ON dr.id = drc."commentableId" AND drc."commentableType" = 'data_request' AND drc.resolved = false
          WHERE dr."projectId" = $1 AND drc.comment IS NOT NULL
          ORDER BY edr.esrs, edr.sort
        `, [projectId]);
        const sortedDrGaps = drGaps.sort((a, b) => {
            const esrsSortOrderA = config_1.ESRS_SORT_ORDER[a.esrs] || 999;
            const esrsSortOrderB = config_1.ESRS_SORT_ORDER[b.esrs] || 999;
            if (esrsSortOrderA !== esrsSortOrderB) {
                return esrsSortOrderA - esrsSortOrderB;
            }
            return a.sort - b.sort;
        });
        const xlsBuffer = await (0, export_dr_gaps_excel_1.generateDrGapsExcelReport)(sortedDrGaps);
        return xlsBuffer;
    }
    async findMaterialityStatus(projectId) {
        const project = await this.projectRepository.findOne({
            where: { id: projectId },
            relations: ['materialTopics'],
        });
        const esrsTopics = await this.esrsService.getEsrsTopics();
        return { project, esrsTopics };
    }
    async updateMaterialityStatus(projectId, materialTopics) {
        const project = await this.projectRepository.findOne({
            where: { id: projectId },
        });
        if (!project) {
            throw new Error('Project not found');
        }
        const topicDatapointCounts = await this.esrsTopicDatapointRepository
            .createQueryBuilder('etd')
            .select('etd.esrsDatapointId', 'esrsDatapointId')
            .addSelect('COUNT(*)', 'count')
            .where('etd.esrsTopicId IN (:...topicIds)', {
            topicIds: materialTopics.map((m) => m.esrsTopicId),
        })
            .groupBy('etd.esrsDatapointId')
            .getRawMany();
        const levelHierarchy = {
            [esrs_topic_entity_1.ESRSTopicLevel.TOPIC]: 0,
            [esrs_topic_entity_1.ESRSTopicLevel.SUB_TOPIC]: 1,
            [esrs_topic_entity_1.ESRSTopicLevel.SUB_SUB_TOPIC]: 2,
        };
        const topicDatapointCollection = topicDatapointCounts.reduce((acc, tdc) => {
            acc[tdc.esrsDatapointId] = {
                count: tdc.count,
                level: 0,
                material: false,
            };
            return acc;
        }, {});
        for (const mt of materialTopics) {
            let materialTopic = await this.materialESRSTopicRepository.findOne({
                where: {
                    project: { id: projectId },
                    esrsTopic: { id: mt.esrsTopicId },
                },
            });
            if (materialTopic) {
                materialTopic.active = mt.active;
                await this.materialESRSTopicRepository.save(materialTopic);
            }
            else {
                materialTopic = this.materialESRSTopicRepository.create({
                    projectId: projectId,
                    esrsTopicId: mt.esrsTopicId,
                    active: mt.active,
                });
                await this.materialESRSTopicRepository.save(materialTopic);
            }
            const topicDatapoints = await this.esrsTopicDatapointRepository.find({
                where: { esrsTopicId: mt.esrsTopicId },
                select: {
                    esrsDatapointId: true,
                },
            });
            const esrsDatapointIds = topicDatapoints.map((td) => td.esrsDatapointId);
            if (esrsDatapointIds.length > 0) {
                const datapointRequests = (await this.datapointRequestRepository
                    .createQueryBuilder('datapointRequest')
                    .innerJoinAndSelect('datapointRequest.esrsDatapoint', 'esrsDatapoint')
                    .innerJoin('datapointRequest.dataRequest', 'dataRequest')
                    .where('datapointRequest.esrsDatapointId IN (:...esrsDatapointIds)', {
                    esrsDatapointIds,
                })
                    .andWhere('dataRequest.projectId = :projectId', { projectId })
                    .select([
                    'datapointRequest.id',
                    'datapointRequest.dataRequestId',
                    'datapointRequest.esrsDatapointId',
                    'datapointRequest.status',
                    'esrsDatapoint.optional',
                ])
                    .getMany());
                const concurrency = 10;
                const datapointRequestChunks = (0, common_util_1.chunkArray)(datapointRequests, concurrency);
                for (const chunk of datapointRequestChunks) {
                    await Promise.all(chunk.map(async (dpRequest) => {
                        let dpMateriality = false;
                        const topicDatapoint = topicDatapointCollection[dpRequest.esrsDatapointId];
                        if (levelHierarchy[mt.level] >= topicDatapoint.level) {
                            if (mt.active) {
                                dpMateriality = true;
                                topicDatapoint.material = true;
                            }
                            else if (topicDatapoint.level === levelHierarchy[mt.level]) {
                                dpMateriality = topicDatapoint.material;
                            }
                            else {
                                dpMateriality = false;
                                topicDatapoint.material = false;
                            }
                            topicDatapoint.level = levelHierarchy[mt.level];
                        }
                        if (dpMateriality &&
                            dpRequest.status === datapoint_request_entity_1.DatapointRequestStatus.NotResponded) {
                            dpRequest.status = datapoint_request_entity_1.DatapointRequestStatus.IncompleteData;
                        }
                        else if (dpMateriality) {
                        }
                        else {
                            dpRequest.status = datapoint_request_entity_1.DatapointRequestStatus.NotResponded;
                        }
                        await this.datapointRequestRepository.update({ id: dpRequest.id }, { status: dpRequest.status });
                    }));
                }
                const dataRequestIds = [
                    ...new Set(datapointRequests.map((dp) => dp.dataRequestId)),
                ];
                const dataRequestIdChunks = (0, common_util_1.chunkArray)(dataRequestIds, concurrency);
                for (const chunk of dataRequestIdChunks) {
                    await Promise.all(chunk.map(async (dataRequestId) => {
                        const dataRequest = (await this.dataRequestRepository
                            .createQueryBuilder('dataRequest')
                            .leftJoinAndSelect('dataRequest.datapointRequests', 'datapointRequest')
                            .select([
                            'dataRequest.id',
                            'dataRequest.status',
                            'datapointRequest.id',
                            'datapointRequest.status',
                        ])
                            .where('dataRequest.id = :dataRequestId', { dataRequestId })
                            .getOne());
                        if (dataRequest) {
                            const allNotAnswered = dataRequest.datapointRequests.every((dpRequest) => dpRequest.status === datapoint_request_entity_1.DatapointRequestStatus.NotResponded);
                            if (allNotAnswered) {
                                dataRequest.status = data_request_entity_1.DataRequestStatus.NotReported;
                                await this.dataRequestRepository.update({ id: dataRequest.id }, { status: dataRequest.status });
                            }
                            else if (dataRequest.status === data_request_entity_1.DataRequestStatus.NotReported) {
                                dataRequest.status = data_request_entity_1.DataRequestStatus.IncompleteData;
                                await this.dataRequestRepository.update({ id: dataRequest.id }, { status: dataRequest.status });
                            }
                        }
                    }));
                }
            }
            await this.workspaceService.storeActionHistory({
                event: 'materiality_status_updated',
                ref: projectId,
                workspaceId: project.workspaceId,
                versionData: {
                    event: 'materiality_status_updated',
                    doneBy: project.createdBy,
                    data: { materialTopics },
                },
            });
        }
        return await this.findMaterialityStatus(projectId);
    }
    async isDatapointMaterial({ projectId, esrsDatapointId, }) {
        const topicDatapoints = await this.esrsTopicDatapointRepository.find({
            where: { esrsDatapointId },
        });
        const esrsTopicIds = topicDatapoints.map((td) => td.esrsTopicId);
        if (esrsTopicIds.length === 0) {
            return false;
        }
        const materialTopics = await this.materialESRSTopicRepository.find({
            where: {
                project: { id: projectId },
                esrsTopicId: (0, typeorm_2.In)(esrsTopicIds),
                active: true,
            },
        });
        return materialTopics.length > 0;
    }
    async getProjectEsrsTopics({ projectId, esrsTopicIds, }) {
        const projectEsrsTopics = await this.materialESRSTopicRepository.find({
            where: {
                project: { id: projectId },
                esrsTopicId: (0, typeorm_2.In)(esrsTopicIds),
            },
        });
        return projectEsrsTopics;
    }
};
exports.ProjectService = ProjectService;
exports.ProjectService = ProjectService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(project_entity_1.Project)),
    __param(2, (0, typeorm_1.InjectRepository)(comment_entity_1.Comment)),
    __param(3, (0, typeorm_1.InjectRepository)(comment_generation_entity_1.CommentGeneration)),
    __param(4, (0, typeorm_1.InjectRepository)(data_request_entity_1.DataRequest)),
    __param(5, (0, typeorm_1.InjectRepository)(material_esrs_topic_entity_1.MaterialESRSTopic)),
    __param(6, (0, typeorm_1.InjectRepository)(esrs_topic_datapoint_entity_1.ESRSTopicDatapoint)),
    __param(7, (0, typeorm_1.InjectRepository)(datapoint_request_entity_1.DatapointRequest)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        esrs_service_1.EsrsService,
        workspace_service_1.WorkspaceService,
        llm_rate_limiter_service_1.LlmRateLimiterService,
        prompts_service_1.PromptService,
        typeorm_2.DataSource])
], ProjectService);
//# sourceMappingURL=project.service.js.map