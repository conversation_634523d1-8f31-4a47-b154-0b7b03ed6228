import { DatapointRequestService } from './datapoint-request.service';
import type { GenerateDatapointRequestTextPayload } from './entities/datapoint-request.dto';
import { datapointGenerationStatus } from './entities/datapoint-generation.entity';
import { DatapointDataRequestSharedService } from 'src/shared/shared-datapoint-datarequest.service';
import { DatapointRequest } from './entities/datapoint-request.entity';
import { User } from 'src/users/entities/user.entity';
import { DatapointCitationService } from './datapoint-citation.service';
import { DatapointStatusService } from './datapoint-status.service';
import { DatapointDocumentService } from './datapoint-document.service';
import { MaterialTopicsService } from './material-topics.service';
export declare class DatapointRequestController {
    private readonly datapointRequestService;
    private readonly datapointCitationService;
    private readonly datapointDocumentService;
    private readonly datapointStatusService;
    private readonly materialTopicsService;
    private readonly datapointDataRequestSharedService;
    constructor(datapointRequestService: DatapointRequestService, datapointCitationService: DatapointCitationService, datapointDocumentService: DatapointDocumentService, datapointStatusService: DatapointStatusService, materialTopicsService: MaterialTopicsService, datapointDataRequestSharedService: DatapointDataRequestSharedService);
    getDataRequest(datapointRequestId: string): Promise<import("./entities/datapoint-request.dto").DatapointRequestData>;
    getMaterialTopics(datapointRequestId: string): Promise<any>;
    updateDatapointRequestStatus(datapointRequestId: string, updateDatapointRequestPayload: Partial<DatapointRequest>, req: any): Promise<DatapointRequest>;
    updateDatapointRequestResponsiblePerson(datapointRequestId: string, updatePayload: {
        responsiblePersonId: string;
    }, req: any): Promise<DatapointRequest>;
    updateDatapointRequestContent(datapointRequestId: string, updateDatapointRequestPayload: Partial<DatapointRequest>, req: any): Promise<DatapointRequest>;
    reviewContentWithAi(datapointRequestId: string, req: any): Promise<void>;
    generateContentWithAi(datapointRequestId: string, additionalData: GenerateDatapointRequestTextPayload, req: any): Promise<void>;
    getDataRequestCitations(datapointRequestId: string, { citationId }: {
        citationId: string;
    }, req: any): Promise<any>;
    updateDataRequestCitations(datapointRequestId: string, payload: {
        citationId: string;
        index: number;
    }, req: any): Promise<DatapointRequest>;
    updateDatapointGenerationStatus(req: any, payload: {
        datapointGenerationId: string;
        status: datapointGenerationStatus;
        evaluatorComment?: string;
    }): Promise<{
        content?: string;
        status: datapointGenerationStatus;
        evaluator?: User;
        evaluatedAt?: Date;
        evaluatorComment?: string;
    }>;
    assessDatapointScore(payload: {
        datapointGenerationId: string;
        textContent: string;
    }): Promise<any>;
    getDocumentLinksForDatapointRequest(datapointRequestId: string): Promise<import("../datapoint-document-chunk/entities/datapoint-document-chunk.entity").DatapointDocumentChunk[]>;
}
