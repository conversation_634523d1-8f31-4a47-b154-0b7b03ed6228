"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const auth_module_1 = require("./auth/auth.module");
const users_module_1 = require("./users/users.module");
const workspace_module_1 = require("./workspace/workspace.module");
const core_1 = require("@nestjs/core");
const auth_guard_1 = require("./auth/auth.guard");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const env_helper_1 = require("./env-helper");
const esrs_module_1 = require("./esrs/esrs.module");
const document_module_1 = require("./document/document.module");
const prompts_module_1 = require("./prompts/prompts.module");
const datapoint_document_chunk_module_1 = require("./datapoint-document-chunk/datapoint-document-chunk.module");
const project_module_1 = require("./project/project.module");
const datapoint_request_module_1 = require("./datapoint/datapoint-request.module");
const data_request_module_1 = require("./data-request/data-request.module");
const logger_middleware_1 = require("./middleware/logger.middleware");
const email_module_1 = require("./external/email.module");
const bull_1 = require("@nestjs/bull");
const cron_module_1 = require("./cron/cron.module");
const nestjs_1 = require("@bull-board/nestjs");
const express_1 = require("@bull-board/express");
const bullAdapter_1 = require("@bull-board/api/bullAdapter");
const queue_module_1 = require("./process-queue/queue.module");
const prompt_management_module_1 = require("./prompt-management/prompt-management.module");
const health_module_1 = require("./health/health.module");
const events_module_1 = require("./events/events.module");
const cache_manager_1 = require("@nestjs/cache-manager");
const redisStore = require("cache-manager-redis-store");
const jobs_1 = require("./types/jobs");
const llm_module_1 = require("./llm/llm.module");
let AppModule = class AppModule {
    configure(consumer) {
        consumer.apply(logger_middleware_1.LoggerMiddleware).forRoutes('*');
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: (0, env_helper_1.getEnvFilePath)(),
            }),
            typeorm_1.TypeOrmModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => {
                    const connectionCredentials = {
                        host: (0, env_helper_1.getDBHost)(),
                        port: configService.get('BACKEND_DB_PORT'),
                        username: configService.get('BACKEND_DB_USER'),
                        password: configService.get('BACKEND_DB_PASSWORD'),
                        database: configService.get('BACKEND_DB_NAME'),
                    };
                    return {
                        type: 'postgres',
                        ...connectionCredentials,
                        extra: {
                            max: 8,
                            min: 2,
                            acquire: 30000,
                            idle: 10000,
                            evict: 10000,
                            acquireTimeoutMillis: 30000,
                            idleTimeoutMillis: 10000,
                        },
                        logging: env_helper_1.isDevelopment ? ['error'] : undefined,
                        synchronize: false,
                        autoLoadEntities: true,
                        entities: ['dist/**/*.entity{.ts,.js}'],
                        migrations: ['database/migrations/**/*{.ts,.js}'],
                    };
                },
                dataSourceFactory: async (options) => (0, env_helper_1.createDataSourceWithVectorSupport)(options),
                inject: [config_1.ConfigService],
            }),
            email_module_1.EmailModule,
            config_1.ConfigModule.forRoot(),
            bull_1.BullModule.forRoot({
                redis: {
                    host: (0, env_helper_1.getRedisHost)(),
                    port: 6379,
                },
            }),
            nestjs_1.BullBoardModule.forRoot({
                route: '/api/queues',
                adapter: express_1.ExpressAdapter,
            }),
            nestjs_1.BullBoardModule.forFeature({
                name: jobs_1.JobProcessor.ChunkExtraction,
                adapter: bullAdapter_1.BullAdapter,
            }, {
                name: jobs_1.JobProcessor.ChunkDpLinking,
                adapter: bullAdapter_1.BullAdapter,
            }, {
                name: jobs_1.JobProcessor.DatapointGeneration,
                adapter: bullAdapter_1.BullAdapter,
            }, {
                name: jobs_1.JobProcessor.DatapointReview,
                adapter: bullAdapter_1.BullAdapter,
            }, {
                name: jobs_1.JobProcessor.LlmRequest,
                adapter: bullAdapter_1.BullAdapter,
            }),
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
            workspace_module_1.WorkspaceModule,
            esrs_module_1.EsrsModule,
            document_module_1.DocumentModule,
            prompts_module_1.PromptModule,
            project_module_1.ProjectModule,
            data_request_module_1.DataRequestModule,
            llm_module_1.LlmModule,
            datapoint_request_module_1.DatapointRequestModule,
            datapoint_document_chunk_module_1.DatapointDocumentChunkModule,
            cron_module_1.CronModule,
            queue_module_1.ProcessQueueModule,
            prompt_management_module_1.PromptManagementModule,
            health_module_1.HealthModule,
            events_module_1.EventsModule,
            cache_manager_1.CacheModule.register({
                isGlobal: true,
                store: redisStore,
                host: (0, env_helper_1.getRedisHost)(),
                port: 6379,
            }),
        ],
        providers: [
            {
                provide: core_1.APP_GUARD,
                useClass: auth_guard_1.AuthGuard,
            },
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map