"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MaterialESRSTopic = void 0;
const typeorm_1 = require("typeorm");
const project_entity_1 = require("./project.entity");
const esrs_topic_entity_1 = require("../../esrs/entities/esrs-topic.entity");
let MaterialESRSTopic = class MaterialESRSTopic {
};
exports.MaterialESRSTopic = MaterialESRSTopic;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], MaterialESRSTopic.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], MaterialESRSTopic.prototype, "projectId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => project_entity_1.Project, (project) => project.materialTopics),
    (0, typeorm_1.JoinColumn)({ name: 'projectId' }),
    __metadata("design:type", project_entity_1.Project)
], MaterialESRSTopic.prototype, "project", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    (0, typeorm_1.Index)(),
    __metadata("design:type", Number)
], MaterialESRSTopic.prototype, "esrsTopicId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => esrs_topic_entity_1.ESRSTopic),
    (0, typeorm_1.JoinColumn)({ name: 'esrsTopicId' }),
    __metadata("design:type", esrs_topic_entity_1.ESRSTopic)
], MaterialESRSTopic.prototype, "esrsTopic", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], MaterialESRSTopic.prototype, "active", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], MaterialESRSTopic.prototype, "createdAt", void 0);
exports.MaterialESRSTopic = MaterialESRSTopic = __decorate([
    (0, typeorm_1.Entity)()
], MaterialESRSTopic);
//# sourceMappingURL=material-esrs-topic.entity.js.map