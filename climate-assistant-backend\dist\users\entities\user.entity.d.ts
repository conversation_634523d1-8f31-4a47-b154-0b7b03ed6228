import { UserWorkspace } from './user-workspace.entity';
import { Token } from './token.entity';
import { UserPromptContext } from './user-prompt-context.entity';
import { DataRequest } from '../../data-request/entities/data-request.entity';
import { Comment } from '../../project/entities/comment.entity';
import { Document } from '../../document/entities/document.entity';
import { DatapointGeneration } from '../../datapoint/entities/datapoint-generation.entity';
import { DatapointRequest } from '../../datapoint/entities/datapoint-request.entity';
export declare class User {
    id: string;
    name: string;
    email: string;
    password: string;
    createdAt: Date;
    userWorkspaces: UserWorkspace[];
    tokens: Token[];
    userPromptContext: UserPromptContext;
    dataRequests: DataRequest[];
    datapointRequests: DatapointRequest[];
    comments: Comment[];
    datapointGenerations: DatapointGeneration[];
    documents: Document[];
}
