import { DataSource, Repository } from 'typeorm';
import { PromptService } from 'src/prompts/prompts.service';
import { DocumentChunk } from 'src/document/entities/document-chunk.entity';
import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { DatapointDocumentChunk } from './entities/datapoint-document-chunk.entity';
import { Document } from 'src/document/entities/document.entity';
import { ESRSDisclosureRequirement } from 'src/esrs/entities/esrs-disclosure-requirement.entity';
import { UsersService } from 'src/users/users.service';
import { LlmRateLimiterService } from 'src/llm/services/llm-rate-limiter.service';
export declare class DatapointDocumentChunkService {
    private readonly llmRateLimitService;
    private dataSource;
    private readonly promptService;
    private readonly documentChunkRepository;
    private readonly documentRepository;
    private readonly datapointRequestRepository;
    private readonly datapointDocumentChunkRepository;
    private readonly esrsDisclosureRequirementRepository;
    private readonly userService;
    constructor(llmRateLimitService: LlmRateLimiterService, dataSource: DataSource, promptService: PromptService, documentChunkRepository: Repository<DocumentChunk>, documentRepository: Repository<Document>, datapointRequestRepository: Repository<DatapointRequest>, datapointDocumentChunkRepository: Repository<DatapointDocumentChunk>, esrsDisclosureRequirementRepository: Repository<ESRSDisclosureRequirement>, userService: UsersService);
    private readonly logger;
    private readonly chunkProcessingLimit;
    private readonly drProcessingLimit;
    private esrsDisclosureRequirementsCache;
    private cacheTimestamp;
    private readonly CACHE_TTL;
    linkDocumentChunksToDatapoints(documentId: string, userId: string, maxNumberOfChunks?: number): Promise<void>;
    private getCachedEsrsDisclosureRequirements;
    private processChunk;
    private processDRForChunk;
}
