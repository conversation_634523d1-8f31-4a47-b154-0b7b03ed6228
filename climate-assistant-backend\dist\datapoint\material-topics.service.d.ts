import { Repository } from 'typeorm';
import { ESRSTopicDatapoint } from 'src/esrs/entities/esrs-topic-datapoint.entity';
import { ProjectService } from 'src/project/project.service';
import { ESRSTopic } from 'src/esrs/entities/esrs-topic.entity';
import { DatapointRequestData } from './entities/datapoint-request.dto';
export declare class MaterialTopicsService {
    private readonly esrsTopicDatapointRepository;
    private readonly projectService;
    constructor(esrsTopicDatapointRepository: Repository<ESRSTopicDatapoint>, projectService: ProjectService);
    loadMaterialTopics(datapointRequest: DatapointRequestData): Promise<any>;
    generateHierarchicalListOfTopics({ topicRelations, projectId, material, }: {
        topicRelations: ESRSTopicDatapoint[];
        projectId: string;
        material: boolean;
    }): Promise<ESRSTopic[]>;
}
