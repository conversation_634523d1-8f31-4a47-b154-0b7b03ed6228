import { Injectable } from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, QueryFailedError, Repository } from 'typeorm';
import throat from 'throat';

import { ChatCompletionMessageParam } from 'openai/resources/chat';
import { ChunkMatches, ESRSDatPointLinkResponse, Match } from 'src/types';
import { PromptService } from 'src/prompts/prompts.service';
import { DocumentChunk } from 'src/document/entities/document-chunk.entity';
import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { DatapointDocumentChunk } from './entities/datapoint-document-chunk.entity';
import {
  Document,
  DocumentStatus,
} from 'src/document/entities/document.entity';
import { ESRSDisclosureRequirement } from 'src/esrs/entities/esrs-disclosure-requirement.entity';
import { UsersService } from 'src/users/users.service';
import { LLM_MODELS } from 'src/constants';
import { LlmRateLimiterService } from 'src/llm/services/llm-rate-limiter.service';
import { WorkerLogger } from 'src/shared/logger.service';

@Injectable()
export class DatapointDocumentChunkService {
  constructor(
    private readonly llmRateLimitService: LlmRateLimiterService,
    @InjectDataSource() private dataSource: DataSource,
    private readonly promptService: PromptService,
    @InjectRepository(DocumentChunk)
    private readonly documentChunkRepository: Repository<DocumentChunk>,
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>,
    @InjectRepository(DatapointRequest)
    private readonly datapointRequestRepository: Repository<DatapointRequest>,
    @InjectRepository(DatapointDocumentChunk)
    private readonly datapointDocumentChunkRepository: Repository<DatapointDocumentChunk>,
    @InjectRepository(ESRSDisclosureRequirement)
    private readonly esrsDisclosureRequirementRepository: Repository<ESRSDisclosureRequirement>,
    private readonly userService: UsersService
  ) {}

  private readonly logger = new WorkerLogger(
    DatapointDocumentChunkService.name
  );

  // Increased parallelization limits based on system capabilities
  private readonly chunkProcessingLimit = throat(10);
  private readonly drProcessingLimit = throat(20);

  // Cache for frequently accessed data
  private esrsDisclosureRequirementsCache: ESRSDisclosureRequirement[] = null;
  private cacheTimestamp: number = 0;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  async linkDocumentChunksToDatapoints(
    documentId: string,
    userId: string,
    maxNumberOfChunks?: number
  ): Promise<void> {
    this.logger.log(
      `Start linking Datapoints to Chunks for DocumentId ${documentId}`
    );

    let document: Document;
    let projectId: string;

    try {
      document = await this.documentRepository.findOne({
        where: { id: documentId },
        relations: {
          workspace: {
            projects: true,
          },
        },
      });

      if (!document) {
        this.logger.error(`Document ${documentId} not found in database`);
        return;
      }

      // TODO: create relation between document and project
      projectId = document.workspace?.projects[0]?.id;
      if (!projectId) {
        this.logger.error(`Document ${documentId} does not have a project`);
        // Update status to error
        document.status = DocumentStatus.ErrorProcessing;
        await this.documentRepository.save(document);
        return;
      }

      this.logger.log(
        `Start linking Datapoints to Chunks for DocumentId ${documentId}, ProjectId ${projectId}`
      );
      document.status = DocumentStatus.LinkingData;
      await this.documentRepository.save(document);
    } catch (error) {
      this.logger.error(
        `Failed to initialize document linking for ${documentId}:`,
        error instanceof Error ? error.stack : error
      );
      return;
    }

    this.logger.log(
      `Start linking Datapoints to Chunks for DocumentId ${documentId}, ProjectId ${projectId}`
    );
    document.status = DocumentStatus.LinkingData;
    await this.documentRepository.save(document);

    try {
      // Optimized query - only fetch needed fields
      const documentChunks: DocumentChunk[] =
        await this.documentChunkRepository.find({
          where: {
            documentId: documentId,
            //matchingsJson: IsNull()
          },
          order: {
            page: 'ASC',
          },
          select: ['id', 'content', 'page', 'matchingsJson'], // Only select needed fields
        });

      this.logger.log(
        `${documentId}: Found ${documentChunks.length} Document Chunks for Document`
      );
      const response: ESRSDatPointLinkResponse = {
        inputTokensUsed: 0,
        outputTokensUsed: 0,
        costForDocument: 0,
        totalLinksCreated: 0,
        timeToGenerate: '0s',
        numberOfChunks: documentChunks.length,
        chunks: [],
      };
      const startTime = Date.now();
      const createdAtBatch = new Date();

      const chunksToProcess = documentChunks.slice(
        0,
        maxNumberOfChunks || documentChunks.length
      );

      // Use cached ESRS disclosure requirements if available
      const esrsDisclosrueRequirements =
        await this.getCachedEsrsDisclosureRequirements();
      const globalUser = await this.userService.findGlobalGlacierAIUser();

      // Pre-fetch all datapoint requests for this project to avoid N+1 queries
      const allDatapointRequests = await this.datapointRequestRepository
        .createQueryBuilder('datapointRequest')
        .innerJoinAndSelect('datapointRequest.esrsDatapoint', 'esrsDatapoint')
        .innerJoinAndSelect('datapointRequest.dataRequest', 'dataRequest')
        .where('dataRequest.projectId = :projectId', { projectId })
        .getMany();

      // Create a map for quick lookup
      const datapointRequestMap = new Map<string, DatapointRequest>();
      allDatapointRequests.forEach((dpr) => {
        datapointRequestMap.set(dpr.esrsDatapoint.datapointId, dpr);
      });

      // Batch process chunks for better performance
      const BATCH_SIZE = 20; // Process chunks in batches
      const chunkBatches = [];
      for (let i = 0; i < chunksToProcess.length; i += BATCH_SIZE) {
        chunkBatches.push(chunksToProcess.slice(i, i + BATCH_SIZE));
      }

      // Process batches sequentially to avoid overwhelming the system
      let processedBatches = 0;
      for (const batch of chunkBatches) {
        try {
          this.logger.log(
            `${documentId}: Processing batch ${processedBatches + 1}/${chunkBatches.length} (${batch.length} chunks)`
          );

          const batchPromises = batch.map((chunk) => {
            return this.chunkProcessingLimit(async () => {
              try {
                await this.processChunk(
                  chunk,
                  documentId,
                  esrsDisclosrueRequirements,
                  datapointRequestMap,
                  globalUser?.id || userId,
                  createdAtBatch,
                  response,
                  projectId
                );
              } catch (chunkError) {
                this.logger.error(
                  `${documentId}: Error processing chunk ${chunk.id}:`,
                  chunkError instanceof Error ? chunkError.stack : chunkError
                );
                // Continue processing other chunks even if one fails
              }
            });
          });

          await Promise.all(batchPromises);

          // Save processed chunks in batch
          const chunksToSave = batch.filter((chunk) => chunk.matchingsJson);
          if (chunksToSave.length > 0) {
            await this.documentChunkRepository.save(chunksToSave);
          }

          processedBatches++;
        } catch (batchError) {
          this.logger.error(
            `${documentId}: Error processing batch ${processedBatches + 1}:`,
            batchError instanceof Error ? batchError.stack : batchError
          );
          // Continue with next batch even if current batch has errors
        }
      }

      this.logger.log(
        `TOTAL TOKEN USAGE FOR EXTRACTION ${documentId}: inputTokensUsed ${response.inputTokensUsed}, outputTokensUsed: ${response.outputTokensUsed}, costFordocument: ${response.costForDocument}`
      );
      const endTime = Date.now();
      response.timeToGenerate = `${(endTime - startTime) / 1000}s`;
      document.status = DocumentStatus.LinkingDataFinished;
      await this.documentRepository.save(document);
      this.logger.log(
        `DatapointLinking Finished ${documentId}: ${document.status}`
      );
    } catch (error) {
      // Check if document still exists in the database
      const documentStillExists = await this.documentRepository.findOne({
        where: { id: documentId },
      });

      if (!documentStillExists) {
        this.logger.error(
          `Document ${documentId} was deleted during linking process. Ending processing.`
        );
        return;
      }

      // Enhanced error logging
      this.logger.error(
        `Critical error in linkDocumentChunksToDatapoints for document ${documentId}:`
      );

      if (error instanceof Error) {
        this.logger.error(`Error name: ${error.name}`);
        this.logger.error(`Error message: ${error.message}`);
        this.logger.error(`Error stack: ${error.stack}`);
      } else {
        this.logger.error(`Unknown error type: ${JSON.stringify(error)}`);
      }

      // Check for specific error types
      if (error instanceof QueryFailedError) {
        this.logger.error(`Database query failed: ${error.query}`);
        this.logger.error(
          `Query parameters: ${JSON.stringify(error.parameters)}`
        );
      }

      if (error?.response?.status === 429) {
        this.logger.error('Rate limit exceeded from LLM provider');
      }

      if (error?.code === 'ECONNREFUSED' || error?.code === 'ETIMEDOUT') {
        this.logger.error('Network connection error');
      }

      document.status = DocumentStatus.ErrorProcessing;
      await this.documentRepository.save(document);

      // Re-throw the error to ensure it's properly handled by any calling code
      throw error;
    }
  }

  private async getCachedEsrsDisclosureRequirements(): Promise<
    ESRSDisclosureRequirement[]
  > {
    const now = Date.now();
    if (
      !this.esrsDisclosureRequirementsCache ||
      now - this.cacheTimestamp > this.CACHE_TTL
    ) {
      this.esrsDisclosureRequirementsCache =
        await this.esrsDisclosureRequirementRepository.find({
          relations: ['esrsDatapoints'],
        });
      this.cacheTimestamp = now;
    }
    return this.esrsDisclosureRequirementsCache;
  }

  private async processChunk(
    chunk: DocumentChunk,
    documentId: string,
    esrsDisclosrueRequirements: ESRSDisclosureRequirement[],
    datapointRequestMap: Map<string, DatapointRequest>,
    userId: string,
    createdAtBatch: Date,
    response: ESRSDatPointLinkResponse,
    projectId: string
  ): Promise<void> {
    this.logger.debug(
      `${documentId}: Linking chunk ${chunk.id}, Length: ${chunk.content.length}`
    );

    const chunkContent = chunk.content;

    // Skip chunks that are too small or likely not meaningful
    if (chunkContent.length < 100) {
      this.logger.debug(
        `${documentId}: Skipping chunk ${chunk.id} - content too short`
      );
      return;
    }

    // Directly classifying document chunks to disclosure requirements (DR)
    this.logger.debug(
      `${documentId}: ${chunk.id} Start Disclosure Requirement Classification`
    );

    const disclosureRequirementClassificationChatCompletion: ChatCompletionMessageParam[] =
      [
        {
          role: 'system',
          content:
            this.promptService.generateDisclosureRequirementClassificationReasoningPrompt(
              {
                chunkContent,
                esrsDisclosureRequirements: esrsDisclosrueRequirements,
              }
            ),
        },
      ];

    let disclosureRequirementClassificationResponse;
    try {
      disclosureRequirementClassificationResponse =
        await this.llmRateLimitService.handleRequest({
          model: LLM_MODELS['o4-mini'],
          messages: disclosureRequirementClassificationChatCompletion,
          json: true,
          temperature: 0,
        });
    } catch (llmError) {
      this.logger.error(
        `${documentId}: Chunk ${chunk.id} - LLM request failed for DR classification:`,
        llmError instanceof Error ? llmError.stack : llmError
      );
      // Re-throw to be handled by parent
      throw new Error(
        `LLM request failed for chunk ${chunk.id}: ${llmError?.message || 'Unknown error'}`
      );
    }

    //  If the chunk content contains text that does not meet the OpenAI prompt policy requirements ignore the error and continue.
    if (disclosureRequirementClassificationResponse.status === 400) {
      this.logger.error(
        `${documentId}: Chunk: ${chunk.id} DR Classification failed with status 400: ${JSON.stringify(disclosureRequirementClassificationResponse.response)}`
      );
      return;
    }

    if (!disclosureRequirementClassificationResponse.response) {
      this.logger.error(
        `${documentId}: Chunk ${chunk.id} - Empty response from LLM for DR classification`
      );
      return;
    }

    response.inputTokensUsed +=
      disclosureRequirementClassificationResponse.token.prompt_tokens || 0;
    response.outputTokensUsed +=
      disclosureRequirementClassificationResponse.token.completion_tokens || 0;
    response.costForDocument +=
      disclosureRequirementClassificationResponse.token.total_cost || 0;

    const disclosureRequirementMatches: string[] =
      disclosureRequirementClassificationResponse.response.disclosureRequirementMatches?.map(
        (dr) => {
          // clean up match ID to remove single or doulbe quotes and any spaces
          const cleanMatchedId = dr
            .replace('ESRS 2.', '') // remove ESRS prefix
            .replace('ESRS', '') // remove ESRS prefix
            .replace(/'/g, '') // remove single quotes
            .replace(/"/g, '') // remove double quotes
            .replace(/\s/g, ''); // remove spaces
          return cleanMatchedId;
        }
      ) || [];

    const chunkMatches: ChunkMatches = {
      chunkContent: chunkContent,
      topicMatches: [],
      disclosureRequirementMatches: disclosureRequirementMatches,
      datapointMatches: [],
    };

    if (
      disclosureRequirementMatches &&
      disclosureRequirementMatches.length > 0
    ) {
      this.logger.debug(
        `${documentId}: Chunk: ${chunk.id} DRsMatched: ${JSON.stringify(disclosureRequirementMatches.join(', '))}`
      );

      // Process DRs in parallel instead of sequentially
      const drPromises = disclosureRequirementMatches.map((dr) => {
        return this.drProcessingLimit(async () => {
          try {
            await this.processDRForChunk(
              dr,
              chunk,
              chunkContent,
              documentId,
              esrsDisclosrueRequirements,
              datapointRequestMap,
              userId,
              createdAtBatch,
              response,
              chunkMatches,
              projectId
            );
          } catch (drError) {
            this.logger.error(
              `${documentId}: Chunk ${chunk.id} - Error processing DR ${dr}:`,
              drError instanceof Error ? drError.stack : drError
            );
            // Continue processing other DRs even if one fails
          }
        });
      });

      await Promise.all(drPromises);

      chunk.matchingsJson = JSON.stringify(chunkMatches);
      response.chunks.push(chunkMatches);
    }
  }

  private async processDRForChunk(
    dr: string,
    chunk: DocumentChunk,
    chunkContent: string,
    documentId: string,
    esrsDisclosrueRequirements: ESRSDisclosureRequirement[],
    datapointRequestMap: Map<string, DatapointRequest>,
    userId: string,
    createdAtBatch: Date,
    response: ESRSDatPointLinkResponse,
    chunkMatches: ChunkMatches,
    projectId: string
  ): Promise<void> {
    // Removed unnecessary 500ms delay
    this.logger.debug(
      `${documentId}: Chunk: ${chunk.id} DR Matching started: ${dr}`
    );

    const esrsDisclosureRequirementMatch: ESRSDisclosureRequirement =
      esrsDisclosrueRequirements.find((eDr) => eDr.dr === dr);
    if (!esrsDisclosureRequirementMatch) {
      this.logger.error(
        `${documentId}: Chunk: ${chunk.id} DR: ${dr} not found in the workspace`
      );
      return;
    }
    const esrsDatapointsToMatch = esrsDisclosureRequirementMatch.esrsDatapoints;

    if (esrsDatapointsToMatch?.length > 0) {
      let datapointMatches: Match[] = [];
      let retryCount = 0;
      const maxRetries = 1;

      while (retryCount <= maxRetries) {
        const datapointClassificationChatCompletion: ChatCompletionMessageParam[] =
          [
            {
              role: 'system',
              content:
                this.promptService.generateDatapointClassificationReasoningPrompt(
                  {
                    chunkContent,
                    esrsDatapoints: esrsDatapointsToMatch,
                    disclosureRequirement: esrsDisclosureRequirementMatch,
                  }
                ),
            },
          ];

        let datapointClassificationResponse;
        try {
          datapointClassificationResponse =
            await this.llmRateLimitService.handleRequest({
              model: LLM_MODELS['o4-mini'],
              messages: datapointClassificationChatCompletion,
              json: true,
              temperature: 0,
            });
        } catch (llmError) {
          this.logger.error(
            `${documentId}: Chunk ${chunk.id} DR ${dr} - LLM request failed for datapoint classification:`,
            llmError instanceof Error ? llmError.stack : llmError
          );
          // Re-throw to be handled by parent
          throw new Error(
            `LLM request failed for DR ${dr}: ${llmError?.message || 'Unknown error'}`
          );
        }

        //  If the chunk content contains text that does not meet the OpenAI prompt policy requirements ignore the error and continue.
        if (datapointClassificationResponse.status === 400) {
          this.logger.error(
            `${documentId}: Chunk: ${chunk.id} DR: ${dr} DP Classification failed with status 400: ${JSON.stringify(datapointClassificationResponse.response)}`
          );
          return;
        }

        if (!datapointClassificationResponse.response) {
          this.logger.error(
            `${documentId}: Chunk ${chunk.id} DR ${dr} - Empty response from LLM for datapoint classification`
          );
          return;
        }

        response.inputTokensUsed +=
          datapointClassificationResponse.token.prompt_tokens || 0;
        response.outputTokensUsed +=
          datapointClassificationResponse.token.completion_tokens || 0;
        response.costForDocument +=
          datapointClassificationResponse.token.total_cost || 0;

        datapointMatches =
          datapointClassificationResponse.response.matchedDatapoints || [];

        this.logger.debug(
          `${documentId}: Chunk: ${chunk.id} DR: ${dr} DPs matched: ${JSON.stringify(datapointMatches)}`
        );

        if (datapointMatches && datapointMatches.length > 0) {
          // Track if any match was successful
          let anyMatchSuccessful = false;
          const failedMatches: string[] = [];

          // First pass - check which matches are valid
          for (const datapointMatch of datapointMatches) {
            if (datapointMatch.matchedId) {
              const matchingDatapointRequest = datapointRequestMap.get(
                datapointMatch.matchedId
              );

              if (!matchingDatapointRequest) {
                failedMatches.push(datapointMatch.matchedId);
                this.logger.error(
                  `${documentId}: Chunk: ${chunk.id} Datapoint: ${datapointMatch.matchedId} not found in the workspace`
                );
              } else {
                anyMatchSuccessful = true;
              }
            }
          }

          // If all matches failed and we haven't retried yet, retry
          if (!anyMatchSuccessful && retryCount < maxRetries) {
            retryCount++;
            this.logger.warn(
              `${documentId}: Chunk: ${chunk.id} DR: ${dr} All datapoint matches failed (${failedMatches.join(', ')}). Retrying... (attempt ${retryCount + 1}/${maxRetries + 1})`
            );
            // Clear the failed matches and retry
            datapointMatches = [];
            continue; // Retry the LLM request
          }

          // Process successful matches
          chunkMatches.datapointMatches.push(...datapointMatches);
          this.logger.debug(
            `${documentId}: Chunk: ${chunk.id} DR: ${dr} DPMatched: ${JSON.stringify(datapointMatches.map((dp) => dp.matchedId).join(', '))}`
          );

          // Batch insert datapoint document chunks
          const datapointDocumentChunksToCreate = [];
          const datapointRequestsToUpdate = [];

          for (const datapointMatch of datapointMatches) {
            if (datapointMatch.matchedId) {
              datapointMatch.executedClassificationPrompt = true;
              const matchingDatapointRequest = datapointRequestMap.get(
                datapointMatch.matchedId
              );

              if (!matchingDatapointRequest) {
                // Already logged above, just skip
                continue;
              }

              datapointDocumentChunksToCreate.push({
                documentChunkId: chunk.id,
                datapointRequestId: matchingDatapointRequest.id,
                createdBy: userId,
                createdAt: createdAtBatch,
              });

              if (datapointMatch.mdrTitle) {
                const cleanPolicy = datapointMatch.mdrTitle
                  .replace(/<\/?h6>/g, '')
                  .trim();

                // Check if the policy is already in the customUserRemark
                if (
                  !matchingDatapointRequest.customUserRemark ||
                  !matchingDatapointRequest.customUserRemark.includes(
                    cleanPolicy
                  )
                ) {
                  if (
                    !matchingDatapointRequest.customUserRemark ||
                    matchingDatapointRequest.customUserRemark.length < 12
                  ) {
                    matchingDatapointRequest.customUserRemark =
                      'Write among others about the following Policies/Target(s)/Action(s):' +
                      `\n- ${cleanPolicy}`;
                  } else {
                    matchingDatapointRequest.customUserRemark += `\n- ${cleanPolicy}`;
                  }
                  datapointRequestsToUpdate.push(matchingDatapointRequest);
                }
              }
            }
          }

          // Batch insert datapoint document chunks
          if (datapointDocumentChunksToCreate.length > 0) {
            try {
              const insertResult = await this.datapointDocumentChunkRepository
                .createQueryBuilder()
                .insert()
                .into(DatapointDocumentChunk)
                .values(datapointDocumentChunksToCreate)
                .orIgnore() // Ignore duplicates
                .execute();

              response.totalLinksCreated += insertResult.identifiers.length;

              this.logger.debug(
                `${documentId}: Chunk ${chunk.id} DR ${dr} - Created ${insertResult.identifiers.length} datapoint-document-chunk links`
              );
            } catch (insertError) {
              this.logger.error(
                `${documentId}: Chunk ${chunk.id} DR ${dr} - Batch insert error:`,
                insertError instanceof Error ? insertError.stack : insertError
              );
              // Don't re-throw - continue with other operations
            }
          }

          // Batch update datapoint requests
          if (datapointRequestsToUpdate.length > 0) {
            try {
              await this.datapointRequestRepository.save(
                datapointRequestsToUpdate
              );
              this.logger.debug(
                `${documentId}: Chunk ${chunk.id} DR ${dr} - Updated ${datapointRequestsToUpdate.length} datapoint requests`
              );
            } catch (updateError) {
              this.logger.error(
                `${documentId}: Chunk ${chunk.id} DR ${dr} - Failed to update datapoint requests:`,
                updateError instanceof Error ? updateError.stack : updateError
              );
              // Don't re-throw - this is not critical
            }
          }

          // Break out of retry loop after successful processing
          break;
        } else {
          // No matches returned, break out of retry loop
          break;
        }
      }
    }
  }
}
