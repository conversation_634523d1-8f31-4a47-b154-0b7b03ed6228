"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = void 0;
const typeorm_1 = require("typeorm");
const user_workspace_entity_1 = require("./user-workspace.entity");
const token_entity_1 = require("./token.entity");
const user_prompt_context_entity_1 = require("./user-prompt-context.entity");
const data_request_entity_1 = require("../../data-request/entities/data-request.entity");
const comment_entity_1 = require("../../project/entities/comment.entity");
const document_entity_1 = require("../../document/entities/document.entity");
const datapoint_generation_entity_1 = require("../../datapoint/entities/datapoint-generation.entity");
const datapoint_request_entity_1 = require("../../datapoint/entities/datapoint-request.entity");
let User = class User {
};
exports.User = User;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], User.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100 }),
    __metadata("design:type", String)
], User.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "password", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], User.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => user_workspace_entity_1.UserWorkspace, (userWorkspace) => userWorkspace.user),
    __metadata("design:type", Array)
], User.prototype, "userWorkspaces", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => token_entity_1.Token, (token) => token.user),
    __metadata("design:type", Array)
], User.prototype, "tokens", void 0);
__decorate([
    (0, typeorm_1.OneToOne)(() => user_prompt_context_entity_1.UserPromptContext, (userPromptContext) => userPromptContext.user),
    __metadata("design:type", user_prompt_context_entity_1.UserPromptContext)
], User.prototype, "userPromptContext", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => data_request_entity_1.DataRequest, (dataRequest) => dataRequest.responsiblePerson),
    __metadata("design:type", Array)
], User.prototype, "dataRequests", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => datapoint_request_entity_1.DatapointRequest, (datapointRequest) => datapointRequest.responsiblePerson),
    __metadata("design:type", Array)
], User.prototype, "datapointRequests", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => comment_entity_1.Comment, (comment) => comment.user),
    __metadata("design:type", Array)
], User.prototype, "comments", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => datapoint_generation_entity_1.DatapointGeneration, (datapointGeneration) => datapointGeneration.evaluator),
    __metadata("design:type", Array)
], User.prototype, "datapointGenerations", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => document_entity_1.Document, (document) => document.creator),
    __metadata("design:type", Array)
], User.prototype, "documents", void 0);
exports.User = User = __decorate([
    (0, typeorm_1.Entity)()
], User);
//# sourceMappingURL=user.entity.js.map