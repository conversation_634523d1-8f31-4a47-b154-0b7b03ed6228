import { UsersService } from './users.service';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    getUserPromptSettings(req: any): Promise<{
        context: string;
    }>;
    savePromptSettings(req: any, body: {
        context: string;
    }): Promise<{
        success: boolean;
    }>;
    createUserCompanyWorkspace(body: {
        email: string;
        password: string;
        name: string;
    }): Promise<{
        user: import("./entities/user.entity").User;
        company: import("../workspace/entities/company.entity").Company;
        workspace: import("../workspace/entities/workspace.entity").Workspace;
    }>;
}
