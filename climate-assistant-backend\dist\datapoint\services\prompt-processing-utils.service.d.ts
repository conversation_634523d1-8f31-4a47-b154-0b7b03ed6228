import { ESRSDatapoint } from 'src/datapoint/entities/esrs-datapoint.entity';
import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { ESRSTopic } from 'src/esrs/entities/esrs-topic.entity';
import { Company } from 'src/workspace/entities/company.entity';
import { LLM_MODELS } from 'src/constants';
import { ChatCompletionMessageParam } from 'openai/resources';
import { TimingTracker } from 'src/util/timing-util';
import { DatapointTypeHelper } from '../utils/datapoint-type.helper';
export interface AIProcessingOptions {
    datapointRequestId: string;
    userId: string;
    workspaceId: string;
    dryRun?: boolean;
    testPrompts?: Record<string, {
        prompt: string;
        chainIdentifier: string;
        model: LLM_MODELS;
    }>;
    useExistingReportTextForReference?: boolean;
}
export interface UnifiedPromptVariables {
    esrsDatapoint: {
        datapointId: string;
        name: string;
        lawText: string;
        footnotes?: string;
        lawTextAR?: string;
        footnotesAR?: string;
        esrsDisclosureRequirement: {
            dr: string;
        };
        companyName: string;
        isConditional: boolean;
    };
    language: string;
    reportTextGenerationRules: string;
    generalCompanyProfile: any;
    reportingYear: string;
    currentYear: number;
    hasMaterialTopics: boolean;
    materialTopics?: string;
    mainMaterialTopics?: string;
    hasNonMaterialTopics: boolean;
    nonMaterialTopics?: string;
    [key: string]: any;
}
export interface PromptBuildResult {
    prompts: ChatCompletionMessageParam[];
    promptIds: string[];
}
export declare class PromptProcessingUtilsService {
    private readonly datapointTypeHelper;
    constructor(datapointTypeHelper: DatapointTypeHelper);
    private readonly logger;
    determineFeatureType(esrsDatapoint: ESRSDatapoint, content: string, processType: 'generation' | 'gap-analysis'): string;
    buildUnifiedVariables(datapointRequest: DatapointRequest, esrsDatapoint: ESRSDatapoint, materialTopicsInHierarchy: ESRSTopic[], nonMaterialTopicsInHierarchy: ESRSTopic[], generalCompanyProfile: string, companyDetails?: Company, additionalVariables?: Record<string, any>): UnifiedPromptVariables;
    determineModel(testPrompts?: Record<string, {
        prompt: string;
        chainIdentifier: string;
        model: LLM_MODELS;
    }>, dryRun?: boolean): LLM_MODELS;
    getModelFromDatabase(promptManagementService: any, feature: string, chainIdentifier?: string): Promise<LLM_MODELS>;
    getModelForChain(promptManagementService: any, feature: string, chainIdentifier: string, testPrompts?: Record<string, {
        prompt: string;
        chainIdentifier: string;
        model: LLM_MODELS;
    }>, dryRun?: boolean, fallbackModel?: LLM_MODELS): Promise<LLM_MODELS>;
    extractTimings(timingTracker?: TimingTracker): any;
    logFeatureType(feature: string, esrsDatapoint: ESRSDatapoint, h2Count?: number): void;
}
