"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatapointDocumentChunkModule = void 0;
const common_1 = require("@nestjs/common");
const datapoint_document_chunk_service_1 = require("./datapoint-document-chunk.service");
const datapoint_document_chunk_entity_1 = require("./entities/datapoint-document-chunk.entity");
const typeorm_1 = require("@nestjs/typeorm");
const prompts_service_1 = require("../prompts/prompts.service");
const document_chunk_entity_1 = require("../document/entities/document-chunk.entity");
const document_entity_1 = require("../document/entities/document.entity");
const datapoint_request_entity_1 = require("../datapoint/entities/datapoint-request.entity");
const esrs_disclosure_requirement_entity_1 = require("../esrs/entities/esrs-disclosure-requirement.entity");
const user_entity_1 = require("../users/entities/user.entity");
const users_module_1 = require("../users/users.module");
const llm_module_1 = require("../llm/llm.module");
let DatapointDocumentChunkModule = class DatapointDocumentChunkModule {
};
exports.DatapointDocumentChunkModule = DatapointDocumentChunkModule;
exports.DatapointDocumentChunkModule = DatapointDocumentChunkModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                datapoint_document_chunk_entity_1.DatapointDocumentChunk,
                document_chunk_entity_1.DocumentChunk,
                document_entity_1.Document,
                datapoint_request_entity_1.DatapointRequest,
                esrs_disclosure_requirement_entity_1.ESRSDisclosureRequirement,
                user_entity_1.User,
            ]),
            users_module_1.UsersModule,
            llm_module_1.LlmModule,
        ],
        providers: [datapoint_document_chunk_service_1.DatapointDocumentChunkService, prompts_service_1.PromptService],
        exports: [DatapointDocumentChunkModule, datapoint_document_chunk_service_1.DatapointDocumentChunkService],
        controllers: [],
    })
], DatapointDocumentChunkModule);
//# sourceMappingURL=datapoint-document-chunk.module.js.map