"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateDrGapsExcelReport = generateDrGapsExcelReport;
const ExcelJS = require("exceljs");
const cheerio = require("cheerio");
const llm_response_util_1 = require("../../util/llm-response-util");
const marked_1 = require("marked");
const export_project_excel_1 = require("./export-project-excel");
const spanRegex = /<span[^>]*>(.*?)<\/span>/g;
async function generateDrGapsExcelReport(drGaps) {
    try {
        const workbook = new ExcelJS.Workbook();
        workbook.creator = 'ESG Data Tool';
        workbook.lastModifiedBy = 'ESG Data Tool';
        workbook.created = new Date();
        workbook.modified = new Date();
        const worksheet = workbook.addWorksheet('DR Gaps Report', {
            views: [{ state: 'frozen', xSplit: 0, ySplit: 1 }],
        });
        worksheet.columns = [
            { header: 'ESRS Topic', key: 'esrs', width: 12 },
            { header: 'Disclosure Requirement ID', key: 'drId', width: 20 },
            { header: 'Disclosure Requirement Title', key: 'drTitle', width: 40 },
            { header: 'Report Text', key: 'drText', width: 50 },
            { header: 'GAP Heading', key: 'gapHeading', width: 30 },
            { header: 'GAP Description', key: 'gapDescription', width: 40 },
            { header: 'Recommended Actions', key: 'recommendedActions', width: 40 },
            { header: 'Example Text', key: 'exampleText', width: 40 },
            { header: 'Disclaimer', key: 'disclaimer', width: 40 },
        ];
        const headerRow = worksheet.getRow(1);
        headerRow.height = 25;
        headerRow.font = { bold: true, size: 12 };
        headerRow.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFD3D3D3' },
        };
        headerRow.alignment = { vertical: 'middle', horizontal: 'center' };
        headerRow.eachCell((cell) => {
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
        });
        for (let i = 0; i < drGaps.length; i++) {
            const drGap = drGaps[i];
            const rowIndex = i + 2;
            let dtText = '';
            let gapHeading = '';
            let gapDescription = '';
            let recommendedActions = '';
            let exampleText = '';
            let disclaimer = '';
            let cleanedText = drGap.drText
                .replace(llm_response_util_1.CITATION_CLIENT_REGEX, '$3')
                .replace(llm_response_util_1.MERGED_CELL_REGEX, '')
                .trim();
            const htmlContent = '<meta charset="UTF-8">' + (await (0, marked_1.marked)(cleanedText));
            dtText = (0, export_project_excel_1.convertHtmlToPlainText)(htmlContent);
            if (drGap.drGaps && drGap.drGaps !== '') {
                try {
                    const gapData = parseGapContent(drGap.drGaps);
                    gapHeading = gapData.heading;
                    gapDescription = gapData.description;
                    recommendedActions = gapData.recommendedActions;
                    exampleText = gapData.exampleText;
                    disclaimer = gapData.disclaimer;
                }
                catch (error) {
                    console.error('Error extracting DR gap information:', error);
                    gapDescription = drGap.drGaps;
                }
            }
            const row = worksheet.addRow({
                esrs: drGap.esrs || '',
                drId: drGap.drId || '',
                drTitle: drGap.drTitle || '',
                drText: dtText || '',
                gapHeading,
                gapDescription,
                recommendedActions,
                exampleText,
                disclaimer,
            });
            row.height = 120;
            row.eachCell((cell, colNumber) => {
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' },
                };
                cell.alignment = {
                    wrapText: true,
                    vertical: 'top',
                };
                const INFO_COLUMNS = 4;
                if (colNumber === INFO_COLUMNS + 1 || colNumber === INFO_COLUMNS + 2) {
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FFFFF2CC' },
                    };
                }
                else if (colNumber === INFO_COLUMNS + 3) {
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FFD9E1F2' },
                    };
                }
                else if (colNumber === INFO_COLUMNS + 4) {
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FFE2EFDA' },
                    };
                }
                else if (colNumber === INFO_COLUMNS + 5) {
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FFFCE4D6' },
                    };
                }
            });
        }
        const buffer = await workbook.xlsx.writeBuffer();
        return Buffer.from(buffer);
    }
    catch (error) {
        console.error('Error generating DR gaps Excel report:', error);
        throw new Error(`Failed to generate DR gaps Excel report: ${error.message}`);
    }
}
function parseGapContent(gapHtml) {
    try {
        const $ = cheerio.load(gapHtml);
        const result = {
            heading: '',
            description: '',
            recommendedActions: '',
            exampleText: '',
            disclaimer: '',
        };
        const headingElement = $('h1, h2, h3, strong').first();
        if (headingElement.length > 0) {
            result.heading = headingElement.text().trim();
        }
        const gapP = $('p:contains("Gap Identified:") p, p:contains("Gap Description:") p');
        if (gapP.length > 0) {
            result.description = gapP.text().trim();
        }
        else {
            const firstP = $('p').not(':has(strong)').first();
            if (firstP.length > 0) {
                result.description = firstP.text().trim();
            }
        }
        const actionItems = $('p:contains("Recommended Actions:") + ul li p, p:contains("Recommendations:") + ul li p');
        if (actionItems.length > 0) {
            const items = actionItems
                .map((_, el) => `• ${$(el).text().trim()}`)
                .get();
            result.recommendedActions = items.join('\n');
        }
        const exampleP = $('p:contains("Example Text:") + p, p:contains("Example:") + p');
        if (exampleP.length > 0) {
            result.exampleText = exampleP.text().trim();
        }
        const disclaimerP = $('p:contains("Disclaimer:") p');
        if (disclaimerP.length > 0) {
            result.disclaimer = disclaimerP.text().trim();
        }
        Object.keys(result).forEach((key) => {
            result[key] = result[key].replace(spanRegex, (match, p1) => p1).trim();
        });
        return result;
    }
    catch (error) {
        console.error('Error parsing gap content:', error);
        return {
            heading: '',
            description: gapHtml.replace(/<[^>]*>/g, '').trim(),
            recommendedActions: '',
            exampleText: '',
            disclaimer: '',
        };
    }
}
//# sourceMappingURL=export-dr-gaps-excel.js.map