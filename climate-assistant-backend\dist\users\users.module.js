"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersModule = void 0;
const common_1 = require("@nestjs/common");
const users_service_1 = require("./users.service");
const typeorm_1 = require("@nestjs/typeorm");
const user_entity_1 = require("./entities/user.entity");
const user_prompt_context_entity_1 = require("./entities/user-prompt-context.entity");
const workspace_entity_1 = require("../workspace/entities/workspace.entity");
const user_workspace_entity_1 = require("./entities/user-workspace.entity");
const token_entity_1 = require("./entities/token.entity");
const users_controller_1 = require("./users.controller");
const llm_service_1 = require("../llm/services/llm.service");
const perplexity_service_1 = require("../util/perplexity.service");
const email_service_1 = require("../external/email.service");
const company_entity_1 = require("../workspace/entities/company.entity");
const email_module_1 = require("../external/email.module");
const workspace_service_1 = require("../workspace/workspace.service");
const workspace_module_1 = require("../workspace/workspace.module");
const role_entity_1 = require("./entities/role.entity");
let UsersModule = class UsersModule {
};
exports.UsersModule = UsersModule;
exports.UsersModule = UsersModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                user_entity_1.User,
                user_prompt_context_entity_1.UserPromptContext,
                token_entity_1.Token,
                company_entity_1.Company,
                workspace_entity_1.Workspace,
                user_workspace_entity_1.UserWorkspace,
                role_entity_1.Role,
            ]),
            email_module_1.EmailModule,
            workspace_module_1.WorkspaceModule,
        ],
        providers: [
            users_service_1.UsersService,
            llm_service_1.LlmService,
            perplexity_service_1.PerplexityService,
            email_service_1.EmailService,
            workspace_service_1.WorkspaceService,
        ],
        exports: [users_service_1.UsersService, typeorm_1.TypeOrmModule],
        controllers: [users_controller_1.UsersController],
    })
], UsersModule);
//# sourceMappingURL=users.module.js.map