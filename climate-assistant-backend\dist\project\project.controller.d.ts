import { ProjectService } from './project.service';
import { CreateProjectRequest, UpdateProjectRequest } from './entities/project.dto';
import { CommentType } from './entities/comment.entity';
import { Response } from 'express';
import { ESRSTopicLevel } from 'src/esrs/entities/esrs-topic.entity';
import { CommentStatus } from './entities/comment-generation.entity';
export declare class ProjectController {
    private readonly projectsService;
    constructor(projectsService: ProjectService);
    listAllProjects(req: any): Promise<import("./entities/project.entity").Project[]>;
    createProject(req: any, createProjectRequest: CreateProjectRequest): Promise<import("./entities/project.entity").Project>;
    createBaseStarterProject({ workspaceId, userId, createProjectRequest, }: {
        workspaceId: string;
        userId: string;
        createProjectRequest: CreateProjectRequest;
    }): Promise<import("./entities/project.entity").Project>;
    listEsrsDatapoints(req: any, esrs: string): Promise<import("../datapoint/entities/esrs-datapoint.entity").ESRSDatapoint[]>;
    getProjectById(projectId: string): Promise<import("./entities/project.dto").ProjectData>;
    updateProjectById(req: any, projectId: string, updateProjectRequest: UpdateProjectRequest): Promise<import("./entities/project.entity").Project>;
    deleteProjectById(req: any, projectId: string): Promise<{
        statusCode: number;
        message: string;
    }>;
    commentDataRequest(req: any, data: {
        comment: string;
        commentableType: CommentType;
        commentableId: string;
    }): Promise<import("./entities/comment.entity").Comment>;
    updateCommentDataRequest(req: any, commentId: string, data: {
        comment: string;
    }): Promise<import("./entities/comment.entity").Comment>;
    updateCommentGenerationStatus(req: any, commentId: string, data: {
        status: CommentStatus;
        evaluatorComment: string;
    }): Promise<import("./entities/comment-generation.entity").CommentGeneration>;
    resolveCommentDataRequest(commentId: string, data: {
        resolve: boolean;
    }): Promise<import("./entities/comment.entity").Comment>;
    deleteCommentDataRequest(req: any, commentId: string): Promise<{
        statusCode: number;
        message: string;
    }>;
    generateDocx(projectId: string, res: Response): Promise<void>;
    generateXlsx(projectId: string, res: Response): Promise<void>;
    generateDrGapsXlsx(projectId: string, res: Response): Promise<void>;
    findMaterialityStatus(projectId: string): Promise<{
        project: import("./entities/project.entity").Project;
        esrsTopics: import("src/esrs/entities/esrs-topic.entity").ESRSTopic[];
    }>;
    updateMaterialityStatus(projectId: string, body: {
        materialTopics: {
            esrsTopicId: number;
            level: ESRSTopicLevel;
            active: boolean;
        }[];
    }): Promise<{
        project: import("./entities/project.entity").Project;
        esrsTopics: import("src/esrs/entities/esrs-topic.entity").ESRSTopic[];
        success: boolean;
    }>;
}
